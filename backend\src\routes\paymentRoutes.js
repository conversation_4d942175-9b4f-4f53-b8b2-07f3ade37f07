const router = require("express").Router();
const { isAuthenticated } = require("../middleware/auth");
const paymentController = require("../controllers/paymentController");
const { handleTokenExpiration } = require("../middleware/googleAuth");

router.post(
    "/",
    isAuthenticated,
    handleTokenExpiration,
    paymentController.createPayment
);
router.post("/callback", paymentController.handlePaymentCallback);

module.exports = router;
