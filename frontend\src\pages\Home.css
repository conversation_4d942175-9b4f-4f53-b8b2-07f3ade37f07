/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen,
        Ubuntu, Cantarell, sans-serif;
    line-height: 1.6;
    color: #333;
}

.home-container {
    min-height: 100vh;
    background: #ffffff;
}

.home-container .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    background: white;
    border-bottom: 1px solid #e5e7eb;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95);
}

.home-container .header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 70px;
}

.home-container .logo {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 18px;
    font-weight: 600;
    color: #1f1f1f;
}

.home-container .logo-text {
    background: linear-gradient(135deg, #4285f4 0%, #34a853 50%, #fbbc05 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.home-container .nav-menu {
    display: flex;
    align-items: center;
    gap: 32px;
}

.home-container .nav-menu a {
    color: #374151;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.2s ease;
}

.home-container .nav-menu a:hover {
    color: #667eea;
}

.signin-btn {
    background: #667eea;
    color: white !important;
    padding: 10px 20px;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.2s ease;
}

.signin-btn:hover {
    background: #5a67d8;
    transform: translateY(-1px);
}

.user-menu {
    display: flex;
    align-items: center;
    gap: 16px;
}

.logout-btn {
    background: #ef4444;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    transition: background 0.2s ease;
}

.logout-btn:hover {
    background: #dc2626;
}

/* Hero Section */
.hero {
    padding: 140px 0 100px;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    text-align: center;
}

.hero-content {
    max-width: 800px;
    margin: 0 auto;
}

.hero h1 {
    font-size: 4rem;
    font-weight: 800;
    color: #1a1a1a;
    margin-bottom: 16px;
    letter-spacing: -0.02em;
}

.hero h2 {
    font-size: 2rem;
    font-weight: 400;
    color: #6b7280;
    margin-bottom: 40px;
}

.cta-button {
    display: inline-flex;
    align-items: center;
    gap: 12px;
    background: white;
    border: 2px solid #e5e7eb;
    padding: 16px 32px;
    border-radius: 12px;
    font-size: 18px;
    font-weight: 600;
    color: #374151;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    margin-bottom: 24px;
}

.cta-button:hover {
    border-color: #d1d5db;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.cta-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.cta-button:disabled:hover {
    border-color: #e5e7eb;
    box-shadow: none;
    transform: none;
}

.hero-description {
    font-size: 1.25rem;
    color: #6b7280;
    max-width: 600px;
    margin: 0 auto;
}

/* Features Section */
.features {
    padding: 100px 0;
    background: white;
}

.feature-section {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 80px;
    align-items: center;
    margin-bottom: 120px;
}

.feature-section.reverse {
    direction: rtl;
}

.feature-section.reverse > * {
    direction: ltr;
}

.feature-content h3 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #1a1a1a;
    margin-bottom: 16px;
    line-height: 1.2;
}

.feature-content h4 {
    font-size: 1.25rem;
    font-weight: 400;
    color: #6b7280;
    margin-bottom: 32px;
    line-height: 1.5;
}

.feature-cta {
    background: #667eea;
    color: white;
    border: none;
    padding: 14px 28px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
}

.feature-cta:hover {
    background: #5a67d8;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.feature-image {
    display: flex;
    justify-content: center;
    align-items: center;
}

.feature-placeholder {
    background: #f8fafc;
    border-radius: 16px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: transform 0.3s ease;
}

.feature-placeholder:hover {
    transform: translateY(-4px);
}

/* Footer */
.footer {
    background: #1f2937;
    color: white;
    padding: 60px 0 40px;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 40px;
}

.footer-links,
.footer-contact {
    display: flex;
    gap: 32px;
}

.footer a {
    color: #d1d5db;
    text-decoration: none;
    transition: color 0.2s ease;
}

.footer a:hover {
    color: white;
}

.footer-bottom {
    text-align: center;
    padding-top: 40px;
    border-top: 1px solid #374151;
}

.footer-bottom p {
    color: #9ca3af;
    font-size: 14px;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .feature-section {
        grid-template-columns: 1fr;
        gap: 40px;
        text-align: center;
    }

    .feature-section.reverse {
        direction: ltr;
    }

    .feature-placeholder svg {
        width: 100%;
        height: auto;
        max-width: 400px;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 0 16px;
    }

    .header-content {
        height: 60px;
        flex-wrap: wrap;
    }

    .home-container .nav-menu {
        gap: 16px;
    }

    .home-container .nav-menu a {
        display: none;
    }

    .hero {
        padding: 100px 0 60px;
    }

    .hero h1 {
        font-size: 2.5rem;
        line-height: 1.2;
    }

    .hero h2 {
        font-size: 1.5rem;
        margin-bottom: 32px;
    }

    .cta-button {
        width: 100%;
        max-width: 300px;
        margin: 0 auto 24px auto;
        justify-content: center;
    }

    .feature-content h3 {
        font-size: 2rem;
    }

    .feature-content h4 {
        font-size: 1.125rem;
        line-height: 1.5;
    }

    .final-cta h3 {
        font-size: 2rem;
    }

    .footer-content {
        flex-direction: column;
        gap: 24px;
        text-align: center;
    }

    .footer-links,
    .footer-contact {
        flex-wrap: wrap;
        justify-content: center;
        gap: 16px;
    }
}

@media (max-width: 480px) {
    .hero h1 {
        font-size: 2rem;
    }

    .hero h2 {
        font-size: 1.25rem;
    }

    .cta-button {
        padding: 14px 24px;
        font-size: 16px;
    }

    .feature-content h3 {
        font-size: 1.75rem;
    }

    .features {
        padding: 60px 0;
    }

    .feature-section {
        margin-bottom: 80px;
    }

    .final-cta {
        padding: 60px 0;
    }

    .final-cta h3 {
        font-size: 1.75rem;
    }
}

/* Animation */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.feature-section {
    animation: fadeInUp 0.6s ease-out;
}

.hero-content {
    animation: fadeInUp 0.8s ease-out;
}

/* Smooth scroll */
html {
    scroll-behavior: smooth;
}

/* Notification Styles */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 16px 24px;
    border-radius: 8px;
    font-weight: 600;
    z-index: 1050;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    animation: slideInRight 0.3s ease-out;
    max-width: 400px;
}

.notification.success {
    background: #10b981;
    color: white;
}

.notification.error {
    background: #ef4444;
    color: white;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1100;
}

.loading-spinner {
    background: white;
    padding: 24px 32px;
    border-radius: 12px;
    font-weight: 600;
    color: #374151;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    position: relative;
}

.loading-spinner::after {
    content: '';
    position: absolute;
    top: 50%;
    right: 12px;
    transform: translateY(-50%);
    width: 16px;
    height: 16px;
    border: 2px solid #e5e7eb;
    border-top: 2px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Animations */
@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes spin {
    0% {
        transform: translateY(-50%) rotate(0deg);
    }
    100% {
        transform: translateY(-50%) rotate(360deg);
    }
}
