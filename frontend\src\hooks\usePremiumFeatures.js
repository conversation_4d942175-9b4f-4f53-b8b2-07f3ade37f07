import { useState, useCallback } from 'react';
import { checkFeatureAccess, checkLimit } from '../services/packageService';

export const usePremiumFeatures = (workspace) => {
  const [premiumModalVisible, setPremiumModalVisible] = useState(false);
  const [premiumModalConfig, setPremiumModalConfig] = useState({
    title: '',
    description: '',
    feature: ''
  });

  const showPremiumModal = useCallback((feature, customTitle = '', customDescription = '') => {
    const featureNames = {
      'member': 'Quản lý thành viên',
      'board': 'Tạo nhiều Board',
      'label': 'Quản lý Label',
      'team': 'Tính năng Team'
    };

    const defaultTitles = {
      'member': '<PERSON><PERSON> thêm nhiều thành viên vào workspace của bạn.',
      'board': 'Để tạo nhiều Board cho dự án của bạn.',
      'label': 'Để quản lý Label cho dự án của bạn.',
      'team': 'Để làm việc nhóm với nhiều thành viên.'
    };

    const defaultDescriptions = {
      'member': 'Hãy nâng cấp lên gói Team để quản lý thành viên trong workspace của bạn.',
      'board': 'Hãy nâng cấp lên gói cao hơn để tạo không giới hạn board cho dự án của bạn.',
      'label': 'Hãy nâng cấp lên gói Personal để sử dụng tính năng quản lý Label cho dự án của bạn.',
      'team': 'Hãy nâng cấp lên gói Team để sử dụng đầy đủ tính năng làm việc nhóm.'
    };

    setPremiumModalConfig({
      title: customTitle || defaultTitles[feature],
      description: customDescription || defaultDescriptions[feature],
      feature
    });
    setPremiumModalVisible(true);
  }, []);

  const checkAccess = useCallback((feature) => {
    return checkFeatureAccess(workspace, feature);
  }, [workspace]);

  const checkLimitAccess = useCallback((feature, currentCount) => {
    return checkLimit(workspace, feature, currentCount);
  }, [workspace]);

  const handleFeatureClick = useCallback((feature, currentCount = 0, specificWorkspace = null) => {
    const workspaceToCheck = specificWorkspace || workspace;
    const limitCheck = checkLimit(workspaceToCheck, feature, currentCount);
    
    if (!limitCheck.canProceed) {
      showPremiumModal(feature);
      return false;
    }
    
    return true;
  }, [workspace, showPremiumModal]);

  return {
    premiumModalVisible,
    premiumModalConfig,
    setPremiumModalVisible,
    showPremiumModal,
    checkAccess,
    checkLimitAccess,
    handleFeatureClick
  };
}; 