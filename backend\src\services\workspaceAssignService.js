const {
    WorkspaceAssignee,
    Workspace,
    User,
    Board,
    BoardAssignee,
    TaskList,
    Task,
    TaskAssignee,
} = require("../models");
const notificationService = require("./notificationService");

const assignUserToWorkspace = async (userId, workspaceId) => {
    try {
        const workspace = await Workspace.findByPk(workspaceId, {
            include: [
                {
                    model: User,
                    as: "owner",
                    attributes: ["id", "fullName"],
                },
            ],
        });
        if (!workspace) {
            throw new Error("Workspace not found");
        }

        const user = await User.findByPk(userId);
        if (!user) {
            throw new Error("User not found");
        }

         // Kiểm tra giới hạn member theo package
         const memberCount = await WorkspaceAssignee.count({
            where: { workspaceId: workspaceId }
        });

        const packageConfigs = {
            1: { teamFeature: 0, limitMember: 1 }, 
            2: { teamFeature: 0, limitMember: 1 },
            3: { teamFeature: 1, limitMember: 20 }
        };

        const packageConfig = packageConfigs[workspace.packageId] || packageConfigs[1];

        if (packageConfig.teamFeature === 0) {
            throw new Error(` Vui lòng nâng cấp để mở tính năng này.`);
        }

        if (memberCount >= packageConfig.limitMember) {
            throw new Error(`Đã đạt giới hạn ${packageConfig.limitMember} thành viên trong gói hiện tại. Vui lòng nâng cấp để thêm thành viên.`);
        }

        const workspaceAssignee = await WorkspaceAssignee.create({
            workspaceId,
            userId,
        });

        // Tạo thông báo cho người được thêm vào workspace
        await notificationService.createNotification({
            userId,
            type: "WORKSPACE_INVITATION",
            content: `Bạn được ${workspace.owner.fullName} thêm vào không gian làm việc "${workspace.name}"`,
            relatedId: workspaceId,
            relatedType: "Workspace",
        });

        return workspaceAssignee;
    } catch (error) {
        console.log("Error in assignUserToWorkspace:", error);
        throw new Error("Failed to assign user to workspace");
    }
};

const unassignUserFromWorkspace = async (userId, workspaceId) => {
    try {
        const workspace = await Workspace.findByPk(workspaceId);
        if (!workspace) {
            throw new Error("Workspace not found");
        }

        const user = await User.findByPk(userId);
        if (!user) {
            throw new Error("User not found");
        }

        // Xóa người dùng khỏi workspace
        await WorkspaceAssignee.destroy({ where: { workspaceId, userId } });

        // Tìm tất cả các board trong workspace
        const boards = await Board.findAll({ where: { workspaceId } });

        // Xóa người dùng khỏi tất cả các board trong workspace
        for (const board of boards) {
            await BoardAssignee.destroy({
                where: {
                    board_id: board.id,
                    user_id: userId,
                },
            });
        }

        // Xóa người dùng khỏi tất cả các task assignment trong workspace
        for (const board of boards) {
            // Tìm tất cả tasklist trong board
            const taskLists = await TaskList.findAll({
                where: { boardId: board.id },
            });

            for (const taskList of taskLists) {
                // Tìm tất cả task trong tasklist
                const tasks = await Task.findAll({
                    where: { tasklistId: taskList.id },
                });

                for (const task of tasks) {
                    // Xóa task assignment của người dùng
                    await TaskAssignee.destroy({
                        where: {
                            taskId: task.id,
                            userId: userId,
                        },
                    });
                }
            }
        }
    } catch (error) {
        throw new Error("Failed to unassign user from workspace");
    }
};

const getWorkspaceAssignees = async (workspaceId) => {
    try {
        const workspaceAssignees = await WorkspaceAssignee.findAll({
            where: { workspaceId },
            include: [
                {
                    model: User,
                    as: "user",
                    attributes: ["id", "email", "fullName", "photoUrl"],
                },
            ],
        });
        return workspaceAssignees;
    } catch (error) {
        throw new Error("Failed to get workspace assignees");
    }
};

const getUserAssignedWorkspaces = async (userId) => {
    try {
        const userAssignedWorkspaces = await WorkspaceAssignee.findAll({
            where: { userId },
            include: [
                {
                    model: Workspace,
                    as: "workspace",
                    attributes: ["id", "name", "description"],
                },
            ],
        });
        return userAssignedWorkspaces;
    } catch (error) {
        throw new Error("Failed to get user assigned workspaces");
    }
};

const leaveAssignedWorkspace = async (userId, workspaceId) => {
    try {
        const workspace = await Workspace.findByPk(workspaceId);
        if (!workspace) {
            throw new Error("Workspace not found");
        }

        const user = await User.findByPk(userId);
        if (!user) {
            throw new Error("User not found");
        }

        // Kiểm tra xem người dùng có được assign vào workspace không
        const workspaceAssignee = await WorkspaceAssignee.findOne({
            where: { workspaceId, userId },
        });
        if (!workspaceAssignee) {
            throw new Error("User is not assigned to this workspace");
        }

        // Tìm tất cả các board trong workspace
        const boards = await Board.findAll({ where: { workspaceId } });

        // Xóa người dùng khỏi tất cả các board trong workspace
        for (const board of boards) {
            await BoardAssignee.destroy({
                where: {
                    board_id: board.id,
                    user_id: userId,
                },
            });
        }

        // Xóa người dùng khỏi tất cả các task assignment trong workspace
        for (const board of boards) {
            // Tìm tất cả tasklist trong board
            const taskLists = await TaskList.findAll({
                where: { boardId: board.id },
            });

            for (const taskList of taskLists) {
                // Tìm tất cả task trong tasklist
                const tasks = await Task.findAll({
                    where: { tasklistId: taskList.id },
                });

                for (const task of tasks) {
                    // Xóa task assignment của người dùng
                    await TaskAssignee.destroy({
                        where: {
                            taskId: task.id,
                            userId: userId,
                        },
                    });
                }
            }
        }

        // Xóa người dùng khỏi workspace
        await WorkspaceAssignee.destroy({
            where: { userId, workspaceId },
        });

        return { message: "Successfully left workspace" };
    } catch (error) {
        console.log("Error in leaveAssignedWorkspace:", error);
        throw new Error("Failed to leave assigned workspace");
    }
};

module.exports = {
    assignUserToWorkspace,
    unassignUserFromWorkspace,
    getWorkspaceAssignees,
    getUserAssignedWorkspaces,
    leaveAssignedWorkspace,
};
