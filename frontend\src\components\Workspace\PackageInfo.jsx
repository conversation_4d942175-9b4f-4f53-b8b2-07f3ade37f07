import React from 'react';
import { Tag, Tooltip } from 'antd';
import { CrownOutlined, StarOutlined, TeamOutlined } from '@ant-design/icons';
import { PACKAGE_CONFIGS } from '../../services/packageService';

const PackageInfo = ({ workspace, size = 'small' }) => {
    if (!workspace || !workspace.packageId) {
        return null;
    }

    const packageConfig = PACKAGE_CONFIGS[workspace.packageId];
    if (!packageConfig) {
        return null;
    }

    const getPackageIcon = () => {
        switch (workspace.packageId) {
            case 1:
                return <StarOutlined />;
            case 2:
                return <CrownOutlined />;
            case 3:
                return <TeamOutlined />;
            default:
                return <StarOutlined />;
        }
    };

    const getPackageColor = () => {
        switch (workspace.packageId) {
            case 1:
                return 'default';
            case 2:
                return 'gold';
            case 3:
                return 'blue';
            default:
                return 'default';
        }
    };

    return (
        <Tooltip
            title={`Gói ${packageConfig.name}: ${packageConfig.description}`}
            placement='top'
        >
            <Tag
                icon={getPackageIcon()}
                color={getPackageColor()}
                size={size}
                style={{
                    fontSize: size === 'small' ? '10px' : '12px',
                    padding: size === 'small' ? '0 4px' : '2px 6px'
                }}
            >
                {packageConfig.name}
            </Tag>
        </Tooltip>
    );
};

export default PackageInfo;
