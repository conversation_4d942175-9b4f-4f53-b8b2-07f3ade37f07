import React, { createContext, useContext } from 'react';
import { usePremiumFeatures } from '../hooks/usePremiumFeatures';
import PremiumFeatureModal from '../components/Subscription/PremiumFeatureModal';
import { useNavigate } from 'react-router-dom';
const PremiumFeatureContext = createContext();

export const PremiumFeatureProvider = ({ children, workspace }) => {
    const premiumFeatures = usePremiumFeatures(workspace);
    const navigate = useNavigate();
    return (
        <PremiumFeatureContext.Provider value={premiumFeatures}>
            {children}
            <PremiumFeatureModal
                visible={premiumFeatures.premiumModalVisible}
                onClose={() => premiumFeatures.setPremiumModalVisible(false)}
                title={premiumFeatures.premiumModalConfig.title}
                description={premiumFeatures.premiumModalConfig.description}
                onLearnMore={() => {
                    // Redirect to features page
                    navigate('/features');
                }}
                onTryPremium={() => {
                    // Redirect to pricing page
                    navigate('/pricing');
                }}
            />
        </PremiumFeatureContext.Provider>
    );
};

export const usePremiumFeatureContext = () => {
    const context = useContext(PremiumFeatureContext);
    if (!context) {
        throw new Error(
            'usePremiumFeatureContext must be used within PremiumFeatureProvider'
        );
    }
    return context;
};
