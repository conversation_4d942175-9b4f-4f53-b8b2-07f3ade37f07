const { Op } = require("sequelize");
const { Invoices, User, Workspace } = require("../models");
const dotenv = require("dotenv");
dotenv.config();

let VIETQR_API_URL = "https://api.vietqr.io/v2/generate";

class PaymentService {
    async createPayment(
        userId,
        workspaceId,
        amount,
        packageId,
        packageDuration
    ) {
        try {
            // Tạo hóa đơn
            const invoice = await Invoices.create({
                userId,
                workspaceId,
                amount,
                status: "pending",
                packageId,
                packageDuration,
                packageStartDate: new Date(),
                packageEndDate: new Date(
                    new Date().setMonth(new Date().getMonth() + packageDuration)
                ),
            });

            // Tạo mã qr để thanh toán từ api vietqr
            const response = await fetch(VIETQR_API_URL, {
                method: "POST",
                headers: {
                    "x-client-id": process.env.VIETQR_CLIENT_ID,
                    "x-api-key": process.env.VIETQR_API_KEY,
                    "Content-Type": "application/json",
                },
                body: JSON.stringify({
                    accountNo: "**********", // số tài khoản nhận
                    accountName: "HO LE AN LAP", // tên tài khoản nhận
                    acqId: "970422", // mã ngân hàng (VD: VCB)
                    addInfo: `THANH TOAN DON HANG #${invoice.id}`,
                    amount: amount,
                    template: "compact",
                }),
            });

            const data = await response.json();

            // Trả về data kèm theo invoiceId
            return {
                ...data,
                invoiceId: invoice.id,
            };
        } catch (error) {
            throw new Error(`Lỗi tạo thanh toán: ${error.message}`);
        }
    }

    //Endpoint nhận callback từ VietQR
    async handlePaymentCallback(invoiceId) {
        try {
            //Cập nhật trạng thái của hóa đơn
            const invoice = await Invoices.update(
                { status: "paid" },
                { where: { id: invoiceId } }
            );

            //Cập nhật trạng thái của workspace
            const workspace = await Workspace.update(
                { packageId: invoice.packageId },
                { where: { id: invoice.workspaceId } }
            );
            //Gửi thông báo cho user

            return { message: "Thanh toán thành công" };
        } catch (error) {
            throw new Error(`Lỗi xử lý callback thanh toán: ${error.message}`);
        }
    }
}

module.exports = new PaymentService();
