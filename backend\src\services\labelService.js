const { Label, TaskLabel, Board, Workspace } = require("../models");

class LabelService {
  //Manage Labels
  async createLabel(boardId, name, color) {

    // <PERSON><PERSON><PERSON> tra gi<PERSON>i hạn label theo package của workspace
    const board = await Board.findByPk(boardId);

    const workspace = await Workspace.findByPk(board.workspaceId);

    const packageConfigs = {
      1: { labelFeature: 0 }, //disabled
      2: { labelFeature: 1 }, //enable
      3: { labelFeature: 1 } //enable
    };

    const packageConfig = packageConfigs[workspace.packageId] || packageConfigs[1];
    
    if (packageConfig.labelFeature === 0) {
      throw new Error(` Vui lòng nâng cấp để mở tính năng này.`);
    }
    
    return await Label.create({
      name,
      color,
      boardId,
    });
  }

  async getLabelsByBoardId(boardId) {
    return await Label.findAll({
      where: { boardId },
    });
  }

  async getLabelById(labelId) {
    return await Label.findByPk(labelId);
  }

  async updateLabel(labelId, name, color) {
    return await Label.update({ name, color }, { where: { id: labelId } });
  }

  async deleteLabel(labelId) {
    return await Label.destroy({
      where: { id: labelId },
    });
  }

  //Manage Task Labels
  async addLabelToTask(taskId, labelId) {
    return await TaskLabel.create({
      taskId,
      labelId,
    });
  }

  async removeLabelFromTask(taskId, labelId) {
    return await TaskLabel.destroy({
      where: { taskId, labelId },
    });
  }

  async getLabelsByTaskId(taskId) {
    const taskLabels = await TaskLabel.findAll({
      where: { taskId },
      include: [
        {
          model: Label,
          as: "labels",
        },
      ],
    });

    // Trả về array of labels thay vì TaskLabel objects
    return taskLabels.map((taskLabel) => taskLabel.labels);
  }
}

module.exports = new LabelService();
