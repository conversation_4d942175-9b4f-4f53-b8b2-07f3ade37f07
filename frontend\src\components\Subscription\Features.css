/* Features Page Styles */
.features-container {
    min-height: 100vh;
    background: #ffffff;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen,
        Ubuntu, Cantarell, sans-serif;
    line-height: 1.6;
    color: #333;
}

.features-container .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.features-header {
    background: white;
    border-bottom: 1px solid #e5e7eb;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95);
}

.features-container .header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 70px;
}

.features-container .logo {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 18px;
    font-weight: 600;
    color: #1f1f1f;
    cursor: pointer;
    transition: opacity 0.2s ease;
}

.features-container .logo:hover {
    opacity: 0.8;
}

.features-container .logo-text {
    background: linear-gradient(135deg, #4285f4 0%, #34a853 50%, #fbbc05 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.features-container .nav-menu {
    display: flex;
    align-items: center;
    gap: 32px;
}

.features-container .nav-link {
    color: #374151;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.2s ease;
}

.features-container .nav-link:hover {
    color: #667eea;
}

.features-container .signin-btn {
    background: #667eea;
    color: white;
    padding: 10px 20px;
    border-radius: 8px;
    border: none;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
}

.features-container .signin-btn:hover {
    background: #5a67d8;
    transform: translateY(-1px);
}

/* Hero Section */
.features-hero {
    padding: 140px 0 80px;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    text-align: center;
}

.hero-content h1 {
    font-size: 3.5rem;
    font-weight: 800;
    color: #1a1a1a;
    margin-bottom: 16px;
    letter-spacing: -0.02em;
}

.hero-content p {
    font-size: 1.25rem;
    color: #6b7280;
    max-width: 600px;
    margin: 0 auto;
}

/* Features List */
.features-list {
    padding: 100px 0;
}

.feature-item {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 80px;
    align-items: center;
    margin-bottom: 120px;
    padding: 0 20px;
}

.feature-item.reverse {
    direction: rtl;
}

.feature-item.reverse > * {
    direction: ltr;
}

.feature-content {
    max-width: 500px;
}

.feature-icon {
    margin-bottom: 24px;
}

.feature-content h3 {
    font-size: 2.25rem;
    font-weight: 700;
    color: #1a1a1a;
    margin-bottom: 16px;
    line-height: 1.2;
}

.feature-content p {
    font-size: 1.125rem;
    color: #6b7280;
    margin-bottom: 24px;
    line-height: 1.6;
}

.feature-benefits {
    list-style: none;
    padding: 0;
    margin: 0;
}

.feature-benefits li {
    font-size: 1rem;
    color: #374151;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.feature-benefits li::before {
    content: '✓';
    color: #34a853;
    font-weight: bold;
    font-size: 1.1rem;
}

/* Feature Images */
.feature-image {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px;
}

.feature-placeholder {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 24px;
    width: 100%;
    max-width: 500px;
    min-height: 350px;
    display: flex;
    flex-direction: column;
    position: relative;
}

/* Board Preview */
.board-preview {
    padding: 0;
    overflow: hidden;
}

.features-container .board-header {
    background: #667eea;
    color: white;
    padding: 16px 24px;
    font-weight: 600;
    font-size: 1.125rem;
}

.features-container .board-columns {
    display: flex;
    gap: 16px;
    padding: 24px;
    min-height: 300px;
}

.features-container .board-column {
    flex: 1;
    background: #ffffff;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 16px;
}

.features-container .column-header {
    font-weight: 600;
    color: #374151;
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 1px solid #e2e8f0;
}

.features-container .task-card {
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    padding: 12px;
    margin-bottom: 8px;
    font-size: 0.875rem;
    color: #374151;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.features-container .task-card.add-task {
    background: #f9fafb;
    color: #6b7280;
    border-style: dashed;
    text-align: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.features-container .task-card.add-task:hover {
    background: #f3f4f6;
}

.features-container .task-card.completed {
    background: #f0fdf4;
    border-color: #bbf7d0;
    color: #166534;
}

/* Share Preview */
.share-preview {
    justify-content: center;
    align-items: center;
}

.share-modal {
    background: white;
    border-radius: 12px;
    padding: 32px;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 400px;
}

.share-modal h4 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #374151;
    margin-bottom: 20px;
    text-align: center;
}

.share-link {
    display: flex;
    gap: 8px;
    margin-bottom: 24px;
}

.share-link input {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    font-size: 0.875rem;
    color: #6b7280;
}

.share-link button {
    background: #667eea;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 0.875rem;
    cursor: pointer;
}

.features-container .members-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.features-container .member {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 0;
}

.features-container .avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: #667eea;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.875rem;
}

.features-container .member span {
    font-size: 0.875rem;
    color: #374151;
}

.role {
    margin-left: auto;
    background: #f3f4f6;
    color: #6b7280;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 0.75rem !important;
}

/* Integration Preview */
.integration-preview {
    justify-content: center;
    align-items: center;
    position: relative;
}

.integration-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 24px;
    width: 100%;
    max-width: 300px;
}

.integration-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    padding: 20px;
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    text-align: center;
}

.integration-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    background: #f3f4f6;
}

.integration-icon.google-drive {
    background: linear-gradient(135deg, #4285f4 0%, #34a853 100%);
}

.integration-icon.gmail {
    background: linear-gradient(135deg, #ea4335 0%, #fbbc05 100%);
}

.integration-icon.google-sheets {
    background: linear-gradient(135deg, #34a853 0%, #fbbc05 100%);
}

.integration-icon.google-calendar {
    background: linear-gradient(135deg, #4285f4 0%, #ea4335 100%);
}

.integration-item span {
    font-size: 0.875rem;
    color: #374151;
    font-weight: 500;
}

.center-logo {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    border: 2px solid #e2e8f0;
    border-radius: 50%;
    padding: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Sync Preview */
.sync-preview {
    justify-content: center;
    align-items: center;
}

.features-container .device-grid {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 24px;
    width: 100%;
}

.features-container .device {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
}

.features-container .device.desktop .screen {
    width: 80px;
    height: 50px;
    background: #667eea;
    border-radius: 8px;
    position: relative;
}

.features-container .device.mobile .screen {
    width: 30px;
    height: 50px;
    background: #34a853;
    border-radius: 6px;
}

.features-container .device.tablet .screen {
    width: 50px;
    height: 40px;
    background: #fbbc05;
    border-radius: 6px;
}

.features-container .device-label {
    font-size: 0.75rem;
    color: #6b7280;
    font-weight: 500;
}

.sync-arrows {
    display: flex;
    gap: 8px;
}

.arrow {
    width: 2px;
    height: 20px;
    background: #e2e8f0;
    position: relative;
}

.arrow::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-top: 6px solid #e2e8f0;
}

/* Task Management Preview */
.task-management-preview {
    justify-content: center;
    align-items: center;
}

.task-detail {
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 24px;
    width: 100%;
    max-width: 400px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.task-header {
    margin-bottom: 16px;
}

.task-header h4 {
    font-size: 1.125rem;
    font-weight: 600;
    color: #374151;
    margin-bottom: 8px;
}

.task-labels {
    display: flex;
    gap: 8px;
}

.label {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 500;
}

.label.urgent {
    background: #fef2f2;
    color: #dc2626;
}

.label.design {
    background: #f0f9ff;
    color: #2563eb;
}

.task-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding: 12px 0;
    border-top: 1px solid #f3f4f6;
    border-bottom: 1px solid #f3f4f6;
}

.assignee {
    display: flex;
    align-items: center;
    gap: 8px;
}

.features-container .assignee .avatar {
    width: 24px;
    height: 24px;
    font-size: 0.75rem;
}

.assignee span {
    font-size: 0.875rem;
    color: #374151;
}

.due-date span {
    font-size: 0.875rem;
    color: #6b7280;
}

.task-description {
    font-size: 0.875rem;
    color: #6b7280;
    margin-bottom: 16px;
    line-height: 1.5;
}

.task-actions {
    display: flex;
    gap: 8px;
}

.features-container .btn-small {
    background: #f9fafb;
    border: 1px solid #e2e8f0;
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 0.75rem;
    color: #374151;
    cursor: pointer;
    transition: all 0.2s ease;
}

.features-container .btn-small:hover {
    background: #f3f4f6;
}

/* CTA Section */
.features-cta {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 100px 0;
    text-align: center;
}

.cta-content h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 16px;
}

.cta-content p {
    font-size: 1.25rem;
    margin-bottom: 40px;
    opacity: 0.9;
}

.cta-button {
    display: inline-flex;
    align-items: center;
    gap: 12px;
    background: white;
    color: #374151;
    border: none;
    padding: 16px 32px;
    border-radius: 12px;
    font-size: 18px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.cta-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
}

.cta-note {
    font-size: 0.875rem;
    margin-top: 16px;
    opacity: 0.8;
}

/* Footer */
.features-footer {
    background: #f8fafc;
    padding: 60px 0 30px;
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 60px;
    margin-bottom: 40px;
}

.footer-brand p {
    color: #6b7280;
    margin-top: 12px;
}

.footer-links {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 40px;
}

.footer-column h4 {
    font-size: 1rem;
    font-weight: 600;
    color: #374151;
    margin-bottom: 16px;
}

.footer-column a {
    display: block;
    color: #6b7280;
    text-decoration: none;
    margin-bottom: 8px;
    transition: color 0.2s ease;
}

.footer-column a:hover {
    color: #667eea;
}

.footer-bottom {
    border-top: 1px solid #e2e8f0;
    padding-top: 30px;
    text-align: center;
}

.footer-bottom p {
    color: #6b7280;
    font-size: 0.875rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-content h1 {
        font-size: 2.5rem;
    }

    .hero-content p {
        font-size: 1rem;
    }

    .feature-item {
        grid-template-columns: 1fr;
        gap: 40px;
        margin-bottom: 80px;
    }

    .feature-item.reverse {
        direction: ltr;
    }

    .feature-content h3 {
        font-size: 1.75rem;
    }

    .feature-content p {
        font-size: 1rem;
    }

    .features-container .nav-menu {
        gap: 16px;
    }

    .features-container .nav-link {
        display: none;
    }

    .features-container .container {
        padding: 0 16px;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .footer-links {
        grid-template-columns: 1fr;
        gap: 30px;
    }

    .cta-content h2 {
        font-size: 2rem;
    }

    .cta-content p {
        font-size: 1rem;
    }

    .cta-button {
        font-size: 16px;
        padding: 14px 28px;
    }
}

@media (max-width: 480px) {
    .hero-content h1 {
        font-size: 2rem;
    }

    .feature-content h3 {
        font-size: 1.5rem;
    }

    .feature-placeholder {
        padding: 16px;
        min-height: 250px;
    }

    .features-container .board-columns {
        flex-direction: column;
        gap: 12px;
        padding: 16px;
    }

    .share-modal {
        padding: 20px;
    }

    .integration-grid {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .task-detail {
        padding: 16px;
    }
}
