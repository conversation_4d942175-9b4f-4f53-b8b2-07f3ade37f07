module.exports = (sequelize, DataTypes) => {
    const User = sequelize.define(
        "User",
        {
            id: {
                type: DataTypes.INTEGER,
                primaryKey: true,
                autoIncrement: true,
            },
            email: {
                type: DataTypes.STRING(255),
                allowNull: false,
            },
            fullName: {
                type: DataTypes.STRING(255),
                field: "full_name",
            },
            photoUrl: {
                type: DataTypes.STRING(255),
                field: "photo_url",
            },
            googleUserId: {
                type: DataTypes.STRING(255),
                field: "google_user_id",
            },
            googleAccessToken: {
                type: DataTypes.TEXT,
                field: "google_access_token",
            },
            googleRefreshToken: {
                type: DataTypes.TEXT,
                field: "google_refresh_token",
            },
            role: {
                type: DataTypes.STRING(255),
                defaultValue: "user",
            },
            isTrial: {
                type: DataTypes.BOOLEAN,
                defaultValue: false,
            },
        },
        {
            tableName: "users",
            timestamps: false,
        }
    );

    User.associate = (models) => {
        User.hasMany(models.TaskList, {
            foreignKey: "owner_id",
            as: "taskLists",
        });
        User.hasMany(models.Task, {
            foreignKey: "created_by",
            as: "createdTasks",
        });
        User.hasMany(models.TaskAssignee, {
            foreignKey: "user_id",
            as: "taskAssignees",
        });
        User.belongsToMany(models.Task, {
            through: models.TaskAssignee,
            foreignKey: "user_id",
            otherKey: "task_id",
            as: "assignedTasksViaAssignees",
        });
        User.hasMany(models.TaskComment, {
            foreignKey: "user_id",
            as: "comments",
        });
        User.hasMany(models.TaskAttachment, {
            foreignKey: "uploaded_by",
            as: "uploadedAttachments",
        });
        User.hasMany(models.Workspace, {
            foreignKey: "ownerId",
            as: "ownedWorkspaces",
        });
        User.hasMany(models.Board, {
            foreignKey: "createdBy",
            as: "createdBoards",
        });
        User.hasMany(models.Notification, {
            foreignKey: {
                name: "userId",
                field: "user_id",
            },
            sourceKey: "id",
            as: "notifications",
        });
        User.hasMany(models.Invoices, {
            foreignKey: "userId",
            as: "invoices",
        });
    };

    return User;
};
