const express = require("express");
const router = express.Router();
const invoiceController = require("../controllers/invoiceController");
const { isAuthenticated } = require("../middleware/auth");
const { handleTokenExpiration } = require("../middleware/googleAuth");

// Routes cho invoice
router.post(
    "/",
    isAuthenticated,
    handleTokenExpiration,
    invoiceController.createInvoice
);
router.post(
    "/trial",
    isAuthenticated,
    handleTokenExpiration,
    invoiceController.createInvoiceTrial
);
router.get(
    "/user",
    isAuthenticated,
    handleTokenExpiration,
    invoiceController.getUserInvoices
);
router.get(
    "/workspace/:workspaceId",
    isAuthenticated,
    handleTokenExpiration,
    invoiceController.getWorkspaceInvoices
);
router.get(
    "/workspace/:workspaceId/active-package",
    isAuthenticated,
    handleTokenExpiration,
    invoiceController.getActivePackage
);
router.get(
    "/stats",
    isAuthenticated,
    handleTokenExpiration,
    invoiceController.getInvoiceStats
);
router.get(
    "/:id",
    isAuthenticated,
    handleTokenExpiration,
    invoiceController.getInvoiceById
);

module.exports = router;
