const { User } = require("../models");
const { Op } = require("sequelize");

const searchUsers = async (query, currentUserId = null) => {
    try {
        const whereCondition = {
            email: {
                [Op.like]: `%${query}%`,
            },
        };

        // Loại trừ user hiện tại nếu có
        if (currentUserId) {
            whereCondition.id = {
                [Op.ne]: currentUserId,
            };
        }

        const users = await User.findAll({
            where: whereCondition,
            attributes: ["id", "fullName", "email", "photoUrl"], // Chỉ trả về các field cần thiết
            limit: 10, // Giới hạn kết quả để tránh tải quá nhiều
        });

        return users;
    } catch (error) {
        throw error;
    }
};

const getAllUsers = async () => {
    try {
        const users = await User.findAll();
        return users;
    } catch (error) {
        throw error;
    }
};

const getUserById = async (userId) => {
    try {
        const user = await User.findByPk(userId);
        return user;
    } catch (error) {
        throw error;
    }
};

const getUserByEmail = async (email) => {
    try {
        const user = await User.findOne({ where: { email } });
        return user;
    } catch (error) {
        throw error;
    }
};

module.exports = {
    searchUsers,
    getAllUsers,
    getUserById,
    getUserByEmail,
};
