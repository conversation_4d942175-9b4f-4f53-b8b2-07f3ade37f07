/* Modal container */
.payment-modal .ant-modal-content {
    border-radius: 16px;
    overflow: hidden;
    background: #fff;
    animation: fadeInUp 0.3s ease;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
}

/* Header */
.payment-modal .ant-modal-header {
    background: linear-gradient(135deg, #1890ff 0%, #40a9ff 50%, #096dd9 100%);
    padding: 16px 24px;
    border: none;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}
.payment-modal .ant-modal-title {
    color: white;
    font-weight: 600;
    font-size: 18px;
}

/* Body */
.payment-modal .ant-modal-body {
    padding: 24px;
    text-align: center;
}

/* QR Section */
.qr-section {
    background: white;
    padding: 16px;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    display: inline-block;
    margin-bottom: 20px;
}
.qr-section img {
    width: 200px;
    height: 200px;
    object-fit: contain;
    display: block;
}

/* Transfer Info */
.transfer-info {
    text-align: left;
    margin-top: 12px;
}
.info-row {
    background: #fafafa;
    padding: 12px 16px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    font-size: 15px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.02);
}
.info-row span.icon {
    color: #1890ff;
    font-size: 18px;
    margin-right: 10px;
}
.info-row strong,
.info-row code {
    font-family: "Roboto Mono", monospace;
    letter-spacing: 0.5px;
}

/* Buttons */
.payment-modal .ant-modal-footer {
    border-top: none;
    padding: 16px 24px;
}
.payment-modal .ant-btn {
    height: 40px;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.2s ease;
}
.payment-modal .ant-btn-primary {
    background: #1890ff;
    border-color: #1890ff;
}
.payment-modal .ant-btn-primary:hover {
    background: #40a9ff;
    border-color: #40a9ff;
}
.payment-modal .ant-btn.copied {
    background: #52c41a !important;
    border-color: #52c41a !important;
    color: white;
}

/* Animation */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive */
@media (max-width: 768px) {
    .qr-section img {
        width: 100%;
        height: auto;
    }
    .transfer-info {
        font-size: 14px;
    }
    .info-row {
        padding: 10px;
    }
}
