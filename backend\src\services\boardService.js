const { Board, TaskList, Task, sequelize, Workspace, BoardAssignee } = require('../models');

const getBoardById = async (id) => {
    const board = await Board.findByPk(id);
    return board;
};

const createBoard = async (workspaceId, name, userId) => {
    // Kiểm tra workspace có tồn tại và user có quyền không
    const workspace = await Workspace.findByPk(workspaceId);
    if (!workspace) {
        throw new Error('Workspace không tồn tại');
    }
    if (workspace.ownerId !== userId) {
        throw new Error('Bạn không có quyền tạo board trong workspace này');
    }

    // Kiểm tra giới hạn board theo package
    const boardCount = await Board.count({
        where: { workspaceId: workspaceId }
    });

    const packageConfigs = {
        1: { limitBoard: 5 },
        2: { limitBoard: 0 }, // unlimited
        3: { limitBoard: 0 }  // unlimited
    };

    const packageConfig = packageConfigs[workspace.packageId] || packageConfigs[1];
    
    if (packageConfig.limitBoard > 0 && boardCount >= packageConfig.limitBoard) {
        throw new Error(`Đã đạt giới hạn ${packageConfig.limitBoard} board trong gói hiện tại. Vui lòng nâng cấp để tạo thêm board.`);
    }


    const board = await Board.create({
        name: name || 'New Board',
        createdBy: userId,
        workspaceId: workspaceId
    });
    
    return board;
};

const getBoardsByUserId = async (userId) => {
    const boards = await Board.findAll({
        where: {
            createdBy: userId
        },
        include: [
            {
                model: Workspace,
                as: 'workspace',
                attributes: ['id', 'name', 'description']
            }
        ]
    });
    
    return boards;
};

const getBoards = async (userId, workspaceId = null) => {
    const whereClause = {
        createdBy: userId
    };

    // Filter by workspace if provided
    if (workspaceId) {
        whereClause.workspaceId = workspaceId;
    }

    const boards = await Board.findAll({
        where: whereClause,
        include: [
            {
                model: Workspace,
                as: 'workspace',
                attributes: ['id', 'name', 'description']
            }
        ]
    });
    
    return boards;
};

const updateBoard = async (id, name) => {
    await Board.update({ name }, { where: { id } });
    const updatedBoard = await Board.findByPk(id);
    return updatedBoard;
};

const deleteBoard = async (id) => {
    const transaction = await sequelize.transaction();
    
    try {
        // Kiểm tra board có tồn tại không
        const board = await Board.findByPk(id, { transaction });
        if (!board) {
            await transaction.rollback();
            throw new Error('Board không tồn tại');
        }

        // Xóa tất cả BoardAssignee liên quan đến board này
        await BoardAssignee.destroy({
            where: { board_id: id },
            transaction
        });

        // Lấy tất cả TaskList liên quan đến board này
        const taskLists = await TaskList.findAll({
            where: { board_id: id },
            transaction
        });

        // Xóa tất cả Task liên quan đến từng TaskList
        for (const taskList of taskLists) {
            // Tìm tất cả Task trong TaskList này
            const tasks = await Task.findAll({
                where: { tasklist_id: taskList.id },
                transaction
            });
            
            // Xóa từng Task để trigger cascade delete cho TaskComment, TaskAttachment, TaskAssignee
            for (const task of tasks) {
                await Task.destroy({
                    where: { id: task.id },
                    transaction
                });
            }
        }

        // Xóa tất cả TaskList liên quan đến board
        await TaskList.destroy({
            where: { board_id: id },
            transaction
        });

        // Cuối cùng xóa Board
        await Board.destroy({ 
            where: { id },
            transaction 
        });

        // Commit transaction nếu tất cả thành công
        await transaction.commit();
        
        return { message: 'Board và tất cả dữ liệu liên quan đã được xóa' };
    } catch (error) {
        // Rollback transaction nếu có lỗi
        await transaction.rollback();
        throw error;
    }
};

const getBoardByWorkspaceId = async (workspaceId) => {
    const boards = await Board.findAll({ where: { workspaceId } });
    return boards;
};

module.exports = {
    getBoardById,  
    createBoard,
    getBoards,
    updateBoard,
    deleteBoard,
    getBoardsByUserId,
    getBoardByWorkspaceId
}; 