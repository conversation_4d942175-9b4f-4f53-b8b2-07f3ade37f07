const router = require("express").Router();
const {
    searchUsers,
    getAllUsers,
    getUserById,
    getUserByEmail,
    getCurrentUser,
    checkUserTrial,
} = require("../controllers/userController");
const { isAuthenticated } = require("../middleware/auth");
const { handleTokenExpiration } = require("../middleware/googleAuth");

router.get("/me", isAuthenticated, handleTokenExpiration, getCurrentUser);
router.get("/search", isAuthenticated, handleTokenExpiration, searchUsers);
router.get("/all", isAuthenticated, handleTokenExpiration, getAllUsers);
router.get("/:userId", isAuthenticated, handleTokenExpiration, getUserById);
router.get(
    "/email/:email",
    isAuthenticated,
    handleTokenExpiration,
    getUserByEmail
);

module.exports = router;
