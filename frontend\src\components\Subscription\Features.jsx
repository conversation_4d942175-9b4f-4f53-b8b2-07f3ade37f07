import React from "react";
import { useNavigate } from "react-router-dom";
import { useAuth } from "../../contexts/AuthContext";
import "./Features.css";

const Features = () => {
    const navigate = useNavigate();
    const { isAuthenticated, login } = useAuth();

    const handleLogin = () => {
        login();
    };

    const handleGoHome = () => {
        navigate("/");
    };

    const handleGoToDashboard = () => {
        navigate("/app/dashboard");
    };

    return (
        <div className="features-container">
            {/* Header */}
            <header className="features-header">
                <div className="container">
                    <div className="header-content">
                        <div className="logo" onClick={handleGoHome}>
                            <img
                                src={"/gwtask.png"}
                                style={{ width: "24px", height: "24px" }}
                                alt="GW Tasks"
                            />
                            <span className="logo-text">GW Tasks</span>
                        </div>

                        <nav className="nav-menu">
                            <a href="/" className="nav-link">
                                Trang chủ
                            </a>
                            <a href="/pricing" className="nav-link">
                                Bảng giá
                            </a>
                            {!isAuthenticated ? (
                                <button
                                    onClick={handleLogin}
                                    className="signin-btn"
                                >
                                    Đăng nhập
                                </button>
                            ) : (
                                <button
                                    onClick={handleGoToDashboard}
                                    className="signin-btn"
                                >
                                    Dashboard
                                </button>
                            )}
                        </nav>
                    </div>
                </div>
            </header>

            {/* Hero Section */}
            <section className="features-hero">
                <div className="container">
                    <div className="hero-content">
                        <h1>Các tính năng của GW Tasks</h1>
                        <p>
                            Khám phá toàn bộ tính năng giúp bạn quản lý công
                            việc hiệu quả hơn
                        </p>
                    </div>
                </div>
            </section>

            {/* Features List */}
            <section className="features-list">
                <div className="container">
                    {/* Feature 1: Full Screen Boards */}
                    <div className="feature-item">
                        <div className="feature-content">
                            <div className="feature-icon">
                                <svg
                                    width="48"
                                    height="48"
                                    viewBox="0 0 24 24"
                                    fill="none"
                                >
                                    <rect
                                        x="3"
                                        y="3"
                                        width="18"
                                        height="18"
                                        rx="2"
                                        stroke="#4285F4"
                                        strokeWidth="2"
                                    />
                                    <rect
                                        x="6"
                                        y="6"
                                        width="3"
                                        height="12"
                                        rx="1"
                                        fill="#4285F4"
                                    />
                                    <rect
                                        x="10.5"
                                        y="6"
                                        width="3"
                                        height="8"
                                        rx="1"
                                        fill="#34A853"
                                    />
                                    <rect
                                        x="15"
                                        y="6"
                                        width="3"
                                        height="6"
                                        rx="1"
                                        fill="#FBBC05"
                                    />
                                </svg>
                            </div>
                            <h3>
                                Hoàn thành nhiều việc hơn trên màn hình đầy đủ
                            </h3>
                            <p>
                                Tổ chức danh sách của bạn trong các Board toàn
                                màn hình để luôn kiểm soát công việc của bạn.
                                Giao diện rộng rãi giúp bạn nhìn tổng quan và
                                quản lý task hiệu quả hơn.
                            </p>
                            <ul className="feature-benefits">
                                <li>Giao diện board trực quan, dễ sử dụng</li>
                                <li>Kéo thả task giữa các danh sách</li>
                                <li>Xem nhiều task cùng lúc</li>
                                <li>Tùy chỉnh layout theo ý muốn</li>
                            </ul>
                        </div>
                        <div className="feature-image">
                            <div className="feature-placeholder board-preview">
                                <div className="board-header">Dashboard</div>
                                <div className="board-columns">
                                    <div className="board-column">
                                        <div className="column-header">
                                            Cần làm
                                        </div>
                                        <div className="task-card">Task 1</div>
                                        <div className="task-card">Task 2</div>
                                        <div className="task-card add-task">
                                            + Thêm task
                                        </div>
                                    </div>
                                    <div className="board-column">
                                        <div className="column-header">
                                            Đang làm
                                        </div>
                                        <div className="task-card">Task 3</div>
                                        <div className="task-card add-task">
                                            + Thêm task
                                        </div>
                                    </div>
                                    <div className="board-column">
                                        <div className="column-header">
                                            Hoàn thành
                                        </div>
                                        <div className="task-card completed">
                                            Task 4
                                        </div>
                                        <div className="task-card add-task">
                                            + Thêm task
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Feature 2: Real-time Sharing */}
                    <div className="feature-item reverse">
                        <div className="feature-content">
                            <div className="feature-icon">
                                <svg
                                    width="48"
                                    height="48"
                                    viewBox="0 0 24 24"
                                    fill="none"
                                >
                                    <path
                                        d="18 8a6 6 0 0 1-7.743 5.743L9 12l1.257 1.743A6 6 0 0 1 18 8z"
                                        fill="#34A853"
                                    />
                                    <path
                                        d="6 8a6 6 0 0 0 7.743 5.743L15 12l-1.257 1.743A6 6 0 0 0 6 8z"
                                        fill="#4285F4"
                                    />
                                    <circle
                                        cx="12"
                                        cy="8"
                                        r="2"
                                        fill="#FBBC05"
                                    />
                                </svg>
                            </div>
                            <h3>Chia sẻ task theo thời gian thực</h3>
                            <p>
                                Chia sẻ danh sách Google Tasks của bạn theo thời
                                gian thực với nhóm của bạn bằng một liên kết duy
                                nhất. Mọi người đều cập nhật tiến độ ngay lập
                                tức.
                            </p>
                            <ul className="feature-benefits">
                                <li>Chia sẻ workspace với một click</li>
                                <li>
                                    Cập nhật real-time cho tất cả thành viên
                                </li>
                                <li>Phân quyền truy cập linh hoạt</li>
                                <li>Quản lý thành viên dễ dàng</li>
                            </ul>
                        </div>
                        <div className="feature-image">
                            <div className="feature-placeholder share-preview">
                                <div className="share-modal">
                                    <h4>Chia sẻ Workspace</h4>
                                    <div className="share-link">
                                        <input
                                            type="text"
                                            value="https://gwtasks.com/public/abc123"
                                            readOnly
                                        />
                                        <button>Copy</button>
                                    </div>
                                    <div className="members-list">
                                        <div className="member">
                                            <div className="avatar">A</div>
                                            <span>Anna Nguyen</span>
                                            <span className="role">Admin</span>
                                        </div>
                                        <div className="member">
                                            <div className="avatar">B</div>
                                            <span>Bao Le</span>
                                            <span className="role">Editor</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Feature 3: Google Workspace Integration */}
                    <div className="feature-item">
                        <div className="feature-content">
                            <div className="feature-icon">
                                <svg
                                    width="48"
                                    height="48"
                                    viewBox="0 0 24 24"
                                    fill="none"
                                >
                                    <path
                                        fill="#4285F4"
                                        d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                                    />
                                    <path
                                        fill="#34A853"
                                        d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                                    />
                                    <path
                                        fill="#FBBC05"
                                        d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                                    />
                                    <path
                                        fill="#EA4335"
                                        d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                                    />
                                </svg>
                            </div>
                            <h3>Tích hợp đầy đủ với Google Workspace</h3>
                            <p>
                                Export danh sách task ra Google Sheets, đính kèm
                                file từ Google Drive và email từ Gmail vào task
                                của bạn. Mọi thứ đều kết nối liền mạch.
                            </p>
                            <ul className="feature-benefits">
                                <li>Export task thành Google Sheets</li>
                                <li>Đính kèm file từ Google Drive</li>
                                <li>Liên kết với email Gmail</li>
                                <li>Đồng bộ với Google Calendar</li>
                            </ul>
                        </div>
                        <div className="feature-image">
                            <div className="feature-placeholder integration-preview">
                                <div className="integration-grid">
                                    <div className="integration-item">
                                        <div className="integration-icon google-drive"></div>
                                        <span>Google Drive</span>
                                    </div>
                                    <div className="integration-item">
                                        <div className="integration-icon gmail"></div>
                                        <span>Gmail</span>
                                    </div>
                                    <div className="integration-item">
                                        <div className="integration-icon google-sheets"></div>
                                        <span>Google Sheets</span>
                                    </div>
                                    <div className="integration-item">
                                        <div className="integration-icon google-calendar"></div>
                                        <span>Google Calendar</span>
                                    </div>
                                </div>
                                <div className="center-logo">
                                    <img
                                        src="/gwtask.png"
                                        alt="GW Tasks"
                                        style={{
                                            width: "32px",
                                            height: "32px",
                                        }}
                                    />
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Feature 4: Cross-platform Sync */}
                    <div className="feature-item reverse">
                        <div className="feature-content">
                            <div className="feature-icon">
                                <svg
                                    width="48"
                                    height="48"
                                    viewBox="0 0 24 24"
                                    fill="none"
                                >
                                    <rect
                                        x="2"
                                        y="3"
                                        width="20"
                                        height="14"
                                        rx="2"
                                        stroke="#4285F4"
                                        strokeWidth="2"
                                    />
                                    <path
                                        d="M8 21h8"
                                        stroke="#4285F4"
                                        strokeWidth="2"
                                    />
                                    <path
                                        d="M12 17v4"
                                        stroke="#4285F4"
                                        strokeWidth="2"
                                    />
                                    <circle
                                        cx="18"
                                        cy="6"
                                        r="2"
                                        fill="#34A853"
                                    />
                                    <rect
                                        x="4"
                                        y="19"
                                        width="3"
                                        height="4"
                                        rx="1"
                                        fill="#FBBC05"
                                    />
                                </svg>
                            </div>
                            <h3>Đồng bộ trên mọi thiết bị</h3>
                            <p>
                                GW Tasks luôn đồng bộ với Google Tasks trên
                                Gmail, Calendar và ứng dụng Google Tasks mobile.
                                Bạn có thể truy cập và cập nhật task từ bất kỳ
                                đâu.
                            </p>
                            <ul className="feature-benefits">
                                <li>Đồng bộ với Google Tasks trên mobile</li>
                                <li>Tích hợp với Gmail và Calendar</li>
                                <li>Cập nhật real-time trên mọi thiết bị</li>
                                <li>Offline mode cho desktop</li>
                            </ul>
                        </div>
                        <div className="feature-image">
                            <div className="feature-placeholder sync-preview">
                                <div className="device-grid">
                                    <div className="device desktop">
                                        <div className="screen"></div>
                                        <div className="device-label">
                                            Desktop
                                        </div>
                                    </div>
                                    <div className="sync-arrows">
                                        <div className="arrow"></div>
                                        <div className="arrow"></div>
                                    </div>
                                    <div className="device mobile">
                                        <div className="screen"></div>
                                        <div className="device-label">
                                            Mobile
                                        </div>
                                    </div>
                                    <div className="device tablet">
                                        <div className="screen"></div>
                                        <div className="device-label">
                                            Tablet
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Feature 5: Advanced Task Management */}
                    <div className="feature-item">
                        <div className="feature-content">
                            <div className="feature-icon">
                                <svg
                                    width="48"
                                    height="48"
                                    viewBox="0 0 24 24"
                                    fill="none"
                                >
                                    <rect
                                        x="3"
                                        y="6"
                                        width="18"
                                        height="12"
                                        rx="2"
                                        stroke="#4285F4"
                                        strokeWidth="2"
                                    />
                                    <path
                                        d="M8 2v4M16 2v4M3 10h18"
                                        stroke="#4285F4"
                                        strokeWidth="2"
                                    />
                                    <circle
                                        cx="8"
                                        cy="14"
                                        r="1"
                                        fill="#34A853"
                                    />
                                    <circle
                                        cx="12"
                                        cy="14"
                                        r="1"
                                        fill="#FBBC05"
                                    />
                                    <circle
                                        cx="16"
                                        cy="14"
                                        r="1"
                                        fill="#EA4335"
                                    />
                                </svg>
                            </div>
                            <h3>Quản lý task nâng cao</h3>
                            <p>
                                Với hệ thống label màu sắc, phân công task,
                                deadline và notification thông minh, bạn có thể
                                quản lý project phức tạp một cách hiệu quả.
                            </p>
                            <ul className="feature-benefits">
                                <li>Hệ thống label và tag linh hoạt</li>
                                <li>Phân công task cho thành viên</li>
                                <li>Thiết lập deadline và reminder</li>
                                <li>Notification real-time</li>
                            </ul>
                        </div>
                        <div className="feature-image">
                            <div className="feature-placeholder task-management-preview">
                                <div className="task-detail">
                                    <div className="task-header">
                                        <h4>Thiết kế giao diện mới</h4>
                                        <div className="task-labels">
                                            <span className="label urgent">
                                                Urgent
                                            </span>
                                            <span className="label design">
                                                Design
                                            </span>
                                        </div>
                                    </div>
                                    <div className="task-meta">
                                        <div className="assignee">
                                            <div className="avatar">A</div>
                                            <span>Anna Nguyen</span>
                                        </div>
                                        <div className="due-date">
                                            <span>📅 25/12/2024</span>
                                        </div>
                                    </div>
                                    <div className="task-description">
                                        Thiết kế giao diện người dùng mới cho
                                        trang chủ...
                                    </div>
                                    <div className="task-actions">
                                        <button className="btn-small">
                                            💬 Comments
                                        </button>
                                        <button className="btn-small">
                                            📎 Attachments
                                        </button>
                                        <button className="btn-small">
                                            🔔 Notifications
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            {/* CTA Section */}
            <section className="features-cta">
                <div className="container">
                    <div className="cta-content">
                        <h2>Sẵn sàng bắt đầu với GW Tasks?</h2>
                        <p>
                            Tham gia cùng hàng nghìn người dùng đang quản lý
                            công việc hiệu quả hơn
                        </p>
                        {!isAuthenticated ? (
                            <button
                                className="cta-button"
                                onClick={handleLogin}
                            >
                                <svg
                                    width="20"
                                    height="20"
                                    viewBox="0 0 24 24"
                                    fill="none"
                                >
                                    <path
                                        fill="#4285F4"
                                        d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                                    />
                                    <path
                                        fill="#34A853"
                                        d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                                    />
                                    <path
                                        fill="#FBBC05"
                                        d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                                    />
                                    <path
                                        fill="#EA4335"
                                        d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                                    />
                                </svg>
                                Đăng nhập với Google
                            </button>
                        ) : (
                            <button
                                className="cta-button"
                                onClick={handleGoToDashboard}
                            >
                                <svg
                                    width="20"
                                    height="20"
                                    viewBox="0 0 24 24"
                                    fill="none"
                                    stroke="currentColor"
                                    strokeWidth="2"
                                >
                                    <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z" />
                                    <polyline points="9,22 9,12 15,12 15,22" />
                                </svg>
                                Vào Dashboard
                            </button>
                        )}
                        <p className="cta-note">
                            Miễn phí sử dụng • Không cần thẻ tín dụng
                        </p>
                    </div>
                </div>
            </section>

            {/* Footer */}
            <footer className="features-footer">
                <div className="container">
                    <div className="footer-content">
                        <div className="footer-brand">
                            <div className="logo">
                                <img
                                    src={"/gwtask.png"}
                                    style={{ width: "24px", height: "24px" }}
                                    alt="GW Tasks"
                                />
                                <span className="logo-text">GW Tasks</span>
                            </div>
                            <p>Ứng dụng desktop cho Google Tasks</p>
                        </div>
                        <div className="footer-links">
                            <div className="footer-column">
                                <h4>Sản phẩm</h4>
                                <a href="/">Trang chủ</a>
                                <a href="/features">Tính năng</a>
                                <a href="/pricing">Bảng giá</a>
                            </div>
                            <div className="footer-column">
                                <h4>Hỗ trợ</h4>
                                <a href="#help">Trợ giúp</a>
                                <a href="#contact">Liên hệ</a>
                                <a href="#privacy">Chính sách bảo mật</a>
                            </div>
                        </div>
                    </div>
                    <div className="footer-bottom">
                        <p>&copy; 2024 GW Tasks. Không liên kết với Google.</p>
                    </div>
                </div>
            </footer>
        </div>
    );
};

export default Features;
