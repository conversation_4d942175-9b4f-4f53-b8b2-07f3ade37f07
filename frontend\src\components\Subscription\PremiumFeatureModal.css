/* Premium Feature Modal Styles */
.premium-feature-modal .ant-modal-content {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.premium-feature-modal .ant-modal-body {
  padding: 0;
}

.premium-modal-content {
  padding: 32px;
  text-align: center;
  background: #ffffff;
}

/* Header Styles */
.premium-modal-header {
  margin-bottom: 32px;
}

.premium-modal-title {
  color: #262626;
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 12px !important;
  line-height: 1.3;
}

.premium-modal-description {
  color: #8c8c8c;
  font-size: 16px;
  line-height: 1.5;
  display: block;
}

/* Illustration Styles */
.premium-modal-illustration {
  margin: 40px 0;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 280px;
  position: relative;
}

.boards-stack {
  position: relative;
  width: 320px;
  height: 240px;
  perspective: 1000px;
}

.board {
  position: absolute;
  width: 280px;
  height: 200px;
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  background: #ffffff;
  border: 1px solid #f0f0f0;
  overflow: hidden;
}

.board-3 {
  background: #434343;
  transform: translateX(-20px) translateY(-20px) rotateY(-8deg) rotateX(2deg);
  z-index: 1;
}

.board-2 {
  background: #1890ff;
  transform: translateX(-10px) translateY(-10px) rotateY(-4deg) rotateX(1deg);
  z-index: 2;
}

.board-1 {
  background: #ffffff;
  transform: translateX(0px) translateY(0px) rotateY(0deg) rotateX(0deg);
  z-index: 3;
  position: relative;
}

.board-header {
  padding: 12px 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.board-3 .board-header {
  border-bottom-color: rgba(255, 255, 255, 0.1);
}

.board-2 .board-header {
  border-bottom-color: rgba(255, 255, 255, 0.2);
}

.board-1 .board-header {
  border-bottom-color: #f0f0f0;
}

.board-title {
  font-size: 14px;
  font-weight: 600;
  color: #ffffff;
}

.board-1 .board-title {
  color: #262626;
}

.board-tabs {
  display: flex;
  gap: 4px;
}

.tab {
  width: 24px;
  height: 4px;
  border-radius: 2px;
  background: rgba(255, 255, 255, 0.6);
}

.board-1 .tab {
  background: #d9d9d9;
}

.board-content {
  padding: 16px;
  display: flex;
  gap: 12px;
  height: calc(100% - 45px);
}

.task-column {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.task-item {
  height: 24px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 4px;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.board-1 .task-item {
  background: #fafafa;
  border-color: #f0f0f0;
}

.board-background {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 120px;
  height: 80px;
  background: linear-gradient(135deg, #ff7875 0%, #ffa940 50%, #52c41a 100%);
  opacity: 0.3;
  border-radius: 8px 0 12px 0;
}

/* Footer Styles */
.premium-modal-footer {
  margin-top: 40px;
}

.learn-more-btn {
  color: #1890ff !important;
  font-size: 14px;
  padding: 8px 16px;
  height: auto;
  border: none;
  background: transparent;
}

.learn-more-btn:hover {
  color: #40a9ff !important;
  background: rgba(24, 144, 255, 0.04);
}

.try-premium-btn {
  background: #1890ff;
  border-color: #1890ff;
  font-size: 16px;
  font-weight: 600;
  padding: 12px 32px;
  height: auto;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
}

.try-premium-btn:hover {
  background: #40a9ff;
  border-color: #40a9ff;
  transform: translateY(-1px);
  box-shadow: 0 6px 16px rgba(24, 144, 255, 0.4);
}

/* Responsive */
@media (max-width: 576px) {
  .premium-modal-content {
    padding: 24px 20px;
  }

  .premium-modal-title {
    font-size: 20px;
  }

  .premium-modal-description {
    font-size: 14px;
  }

  .boards-stack {
    width: 280px;
    height: 200px;
  }

  .board {
    width: 240px;
    height: 160px;
  }

  .premium-modal-footer .ant-space {
    flex-direction: column;
    width: 100%;
  }

  .try-premium-btn {
    width: 100%;
  }
}
