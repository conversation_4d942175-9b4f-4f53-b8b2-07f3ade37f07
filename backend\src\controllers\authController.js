const jwt = require("jsonwebtoken");
const dotenv = require("dotenv");
dotenv.config();

//Hi<PERSON>n thị trang đăng nhập
const getLoginPage = (req, res) => {
    try {
        res.status(200).json({
            status: "success",
            data: {
                googleAuthUrl: "/auth/google",
            },
        });
    } catch (error) {
        res.status(500).json({
            status: "error",
            message: "<PERSON><PERSON> lỗi khi xử lý yêu cầu đăng nhập",
        });
    }
};

//Xử lý callback từ Google OAuth
const handleGoogleCallback = async (req, res) => {
    try {
        if (!req.user) {
            // Redirect về frontend với error parameter
            return res.redirect(
                `${process.env.FRONTEND_URL}?error=login_failed`
            );
        }

        // Sử dụng Google Access Token thay vì tạo JWT mới
        const googleAccessToken = req.user.googleAccessToken;

        if (!googleAccessToken) {
            console.error("Không có Google Access Token");
            return res.redirect(
                `${process.env.FRONTEND_URL}?error=no_access_token`
            );
        }

        // Redirect về frontend với Google Access Token
        res.redirect(`${process.env.FRONTEND_URL}?token=${googleAccessToken}`);
    } catch (error) {
        console.error("Lỗi trong handleGoogleCallback:", error);
        // Redirect về frontend với error parameter
        res.redirect(`${process.env.FRONTEND_URL}?error=callback_error`);
    }
};

//Lấy thông tin profile người dùng
const getProfile = async (req, res) => {
    try {
        if (!req.user) {
            return res.status(401).json({
                status: "error",
                message: "Vui lòng đăng nhập",
            });
        }

        res.status(200).json({
            status: "success",
            data: req.user,
        });
    } catch (error) {
        res.status(500).json({
            status: "error",
            message: "Có lỗi khi lấy thông tin người dùng",
        });
    }
};

//Xử lý đăng xuất
const logout = (req, res) => {
    try {
        // Xóa session
        req.logout((err) => {
            if (err) {
                return res.status(500).json({
                    status: "error",
                    message: "Có lỗi khi đăng xuất",
                });
            }
        });
        res.redirect(`${process.env.FRONTEND_URL}`);
    } catch (error) {
        res.status(500).json({
            status: "error",
            message: "Có lỗi khi xử lý đăng xuất",
        });
    }
};

module.exports = {
    getLoginPage,
    handleGoogleCallback,
    getProfile,
    logout,
};
