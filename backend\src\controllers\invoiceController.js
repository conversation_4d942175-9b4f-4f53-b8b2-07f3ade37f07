const invoiceService = require("../services/invoiceService");

class InvoiceController {
    async createInvoice(req, res) {
        try {
            const {
                workspaceId,
                packageId,
                packageDuration,
                amount,
                paymentMethod,
                description,
            } = req.body;
            const userId = req.user.id;

            // Tính toán ngày bắt đầu và kết thúc gói
            const packageStartDate = new Date();
            const packageEndDate = new Date();
            packageEndDate.setMonth(
                packageEndDate.getMonth() + packageDuration
            );

            const invoiceData = {
                userId,
                workspaceId,
                amount,
                packageId,
                packageDuration,
                packageStartDate,
                packageEndDate,
                paymentMethod: paymentMethod || "vnpay",
                description,
            };

            const invoice = await invoiceService.createInvoice(invoiceData);

            res.status(201).json({
                status: "success",
                message: "Tạo hóa đơn thành công",
                data: invoice,
            });
        } catch (error) {
            res.status(400).json({
                status: "error",
                message: error.message,
            });
        }
    }

    async createInvoiceTrial(req, res) {
        try {
            const { workspaceId, packageId } = req.body;
            const userId = req.user.id;
            const invoiceData = {
                userId,
                workspaceId,
                packageId,
            };
            const invoice = await invoiceService.createInvoiceTrial(
                invoiceData
            );

            res.status(201).json({
                status: "success",
                message: "Tạo hóa đơn trial thành công",
                data: invoice,
            });
        } catch (error) {
            res.status(400).json({
                status: "error",
                message: error.message,
            });
        }
    }

    async getInvoiceById(req, res) {
        try {
            const { id } = req.params;
            const invoice = await invoiceService.getInvoiceById(id);

            // Kiểm tra quyền truy cập
            if (invoice.userId !== req.user.id) {
                return res.status(403).json({
                    status: "error",
                    message: "Không có quyền truy cập hóa đơn này",
                });
            }

            res.json({
                status: "success",
                data: invoice,
            });
        } catch (error) {
            res.status(404).json({
                status: "error",
                message: error.message,
            });
        }
    }

    async getUserInvoices(req, res) {
        try {
            const userId = req.user.id;
            const page = parseInt(req.query.page) || 1;
            const limit = parseInt(req.query.limit) || 10;

            const result = await invoiceService.getInvoicesByUser(
                userId,
                page,
                limit
            );

            res.json({
                status: "success",
                data: result,
            });
        } catch (error) {
            res.status(400).json({
                status: "error",
                message: error.message,
            });
        }
    }

    async getWorkspaceInvoices(req, res) {
        try {
            const { workspaceId } = req.params;
            const page = parseInt(req.query.page) || 1;
            const limit = parseInt(req.query.limit) || 10;

            const result = await invoiceService.getInvoicesByWorkspace(
                workspaceId,
                page,
                limit
            );

            res.json({
                status: "success",
                data: result,
            });
        } catch (error) {
            res.status(400).json({
                status: "error",
                message: error.message,
            });
        }
    }

    async getActivePackage(req, res) {
        try {
            const { workspaceId } = req.params;
            const activePackage = await invoiceService.getActivePackage(
                workspaceId
            );

            res.json({
                status: "success",
                data: activePackage,
            });
        } catch (error) {
            res.status(400).json({
                status: "error",
                message: error.message,
            });
        }
    }

    async getInvoiceStats(req, res) {
        try {
            const userId = req.user.id;
            const { workspaceId } = req.query;

            const stats = await invoiceService.getInvoiceStats(
                userId,
                workspaceId
            );

            res.json({
                status: "success",
                data: stats,
            });
        } catch (error) {
            res.status(400).json({
                status: "error",
                message: error.message,
            });
        }
    }
}

module.exports = new InvoiceController();
