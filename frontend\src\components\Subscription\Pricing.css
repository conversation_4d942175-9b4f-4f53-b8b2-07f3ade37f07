/* Pricing Page Styles */
.pricing-container {
    min-height: 100vh;
    background: #ffffff;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen,
        Ubuntu, Cantarell, sans-serif;
    line-height: 1.6;
    color: #333;
}

.pricing-container .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.pricing-header {
    background: white;
    border-bottom: 1px solid #e5e7eb;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95);
}

.pricing-container .header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 70px;
}

.pricing-container .logo {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 18px;
    font-weight: 600;
    color: #1f1f1f;
    cursor: pointer;
    transition: opacity 0.2s ease;
}

.pricing-container .logo:hover {
    opacity: 0.8;
}

.pricing-container .logo-text {
    background: linear-gradient(135deg, #4285f4 0%, #34a853 50%, #fbbc05 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.pricing-container .nav-menu {
    display: flex;
    align-items: center;
    gap: 32px;
}

.pricing-container .nav-link {
    color: #374151;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.2s ease;
}

.pricing-container .nav-link:hover {
    color: #667eea;
}

.pricing-container .signin-btn {
    background: #667eea;
    color: white;
    padding: 10px 20px;
    border-radius: 8px;
    border: none;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
}

.pricing-container .signin-btn:hover {
    background: #5a67d8;
    transform: translateY(-1px);
}

/* Hero Section */
.pricing-hero {
    padding: 140px 0 80px;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    text-align: center;
}

.hero-content h1 {
    font-size: 3.5rem;
    font-weight: 800;
    color: #1a1a1a;
    margin-bottom: 16px;
    letter-spacing: -0.02em;
}

.hero-content > p {
    font-size: 1.25rem;
    color: #6b7280;
    max-width: 600px;
    margin: 0 auto 40px;
}

/* Billing Toggle */
.billing-toggle {
    display: inline-flex;
    background: white;
    border-radius: 12px;
    padding: 4px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
}

.billing-toggle label {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 500;
    color: #6b7280;
    transition: all 0.2s ease;
    position: relative;
}

.billing-toggle label.active {
    background: #667eea;
    color: white;
    box-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);
}

.billing-toggle input[type="radio"] {
    display: none;
}

.discount-badge {
    background: #34a853;
    color: white;
    font-size: 0.75rem;
    padding: 2px 6px;
    border-radius: 4px;
    font-weight: 600;
    margin-left: 8px;
}

.billing-toggle label:not(.active) .discount-badge {
    background: #f3f4f6;
    color: #6b7280;
}

/* Pricing Plans */
.pricing-plans {
    padding: 80px 0;
}

.plans-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 32px;
    margin-top: 40px;
}

.pricing-container .plan-card {
    background: white;
    border: 2px solid #e2e8f0;
    border-radius: 16px;
    padding: 32px;
    position: relative;
    transition: all 0.3s ease;
}

.pricing-container .plan-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.pricing-container .plan-card.popular {
    border-color: #667eea;
    transform: scale(1.05);
    box-shadow: 0 16px 32px rgba(102, 126, 234, 0.2);
}

.pricing-container .plan-card.popular:hover {
    transform: scale(1.05) translateY(-4px);
}

.popular-badge {
    position: absolute;
    top: -12px;
    left: 50%;
    transform: translateX(-50%);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 8px 24px;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
}

.pricing-container .plan-header {
    text-align: center;
    margin-bottom: 32px;
}

.pricing-container .plan-header h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1a1a1a;
    margin-bottom: 8px;
}

.pricing-container .plan-description {
    color: #6b7280;
    margin-bottom: 24px;
}

.pricing-container .plan-price {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
}

.pricing-container .price {
    font-size: 2rem;
    font-weight: 700;
    color: #1a1a1a;
}

.savings {
    background: #dcfce7;
    color: #166534;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
}

/* Plan Features */
.pricing-container .plan-features {
    margin-bottom: 32px;
}

.pricing-container .plan-features ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.pricing-container .plan-features li {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 0;
    font-size: 0.875rem;
}

.feature-included {
    color: #374151;
}

.feature-excluded {
    color: #9ca3af;
}

.pricing-container .plan-features svg {
    flex-shrink: 0;
}

/* Plan Action */
.pricing-container .plan-action {
    text-align: center;
}

.pricing-container .plan-button {
    width: 100%;
    padding: 16px 24px;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    border: none;
}

.pricing-container .plan-button.primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.pricing-container .plan-button.primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
}

.pricing-container .plan-button.secondary {
    background: white;
    color: #667eea;
    border: 2px solid #667eea;
}

.pricing-container .plan-button.secondary:hover {
    background: #667eea;
    color: white;
    transform: translateY(-2px);
}

.pricing-container .plan-note {
    font-size: 0.875rem;
    color: #6b7280;
    margin-top: 12px;
}

/* Feature Comparison */
.feature-comparison {
    padding: 80px 0;
    background: #f8fafc;
}

.feature-comparison h2 {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 700;
    color: #1a1a1a;
    margin-bottom: 48px;
}

.pricing-container .comparison-table {
    background: white;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.pricing-container .comparison-table table {
    width: 100%;
    border-collapse: collapse;
}

.pricing-container .comparison-table th,
.pricing-container .comparison-table td {
    padding: 16px 24px;
    text-align: left;
    border-bottom: 1px solid #e2e8f0;
}

.pricing-container .comparison-table th {
    background: #f8fafc;
    font-weight: 600;
    color: #374151;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.pricing-container .comparison-table td {
    color: #6b7280;
    font-size: 0.875rem;
}

.pricing-container .comparison-table tr:hover {
    background: #f9fafb;
}

.pricing-container .comparison-table td:first-child {
    font-weight: 500;
    color: #374151;
}

/* FAQ Section */
.pricing-faq {
    padding: 80px 0;
}

.pricing-faq h2 {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 700;
    color: #1a1a1a;
    margin-bottom: 48px;
}

.pricing-container .faq-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 32px;
}

.pricing-container .faq-item {
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 24px;
    transition: all 0.2s ease;
}

.pricing-container .faq-item:hover {
    border-color: #667eea;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.1);
}

.pricing-container .faq-item h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: #374151;
    margin-bottom: 12px;
}

.pricing-container .faq-item p {
    color: #6b7280;
    line-height: 1.6;
}

/* CTA Section */
.pricing-cta {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 100px 0;
    text-align: center;
}

.cta-content h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 16px;
}

.cta-content > p {
    font-size: 1.25rem;
    margin-bottom: 40px;
    opacity: 0.9;
}

.cta-buttons {
    display: flex;
    justify-content: center;
    gap: 16px;
    margin-bottom: 16px;
}

.cta-button {
    display: inline-flex;
    align-items: center;
    gap: 12px;
    padding: 16px 32px;
    border-radius: 12px;
    font-size: 18px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    border: none;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.cta-button.primary {
    background: white;
    color: #374151;
}

.cta-button.primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
}

.cta-note {
    font-size: 0.875rem;
    opacity: 0.8;
}

/* Footer */
.pricing-footer {
    background: #f8fafc;
    padding: 60px 0 30px;
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 60px;
    margin-bottom: 40px;
}

.footer-brand p {
    color: #6b7280;
    margin-top: 12px;
}

.footer-links {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 40px;
}

.footer-column h4 {
    font-size: 1rem;
    font-weight: 600;
    color: #374151;
    margin-bottom: 16px;
}

.footer-column a {
    display: block;
    color: #6b7280;
    text-decoration: none;
    margin-bottom: 8px;
    transition: color 0.2s ease;
}

.footer-column a:hover {
    color: #667eea;
}

.footer-bottom {
    border-top: 1px solid #e2e8f0;
    padding-top: 30px;
    text-align: center;
}

.footer-bottom p {
    color: #6b7280;
    font-size: 0.875rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-content h1 {
        font-size: 2.5rem;
    }

    .hero-content > p {
        font-size: 1rem;
    }

    .billing-toggle {
        flex-direction: column;
        width: 100%;
        max-width: 300px;
    }

    .billing-toggle label {
        justify-content: center;
        width: 100%;
    }

    .plans-grid {
        grid-template-columns: 1fr;
        gap: 24px;
    }

    .pricing-container .plan-card.popular {
        transform: none;
    }

    .pricing-container .plan-card.popular:hover {
        transform: translateY(-4px);
    }

    .pricing-container .comparison-table {
        overflow-x: auto;
    }

    .pricing-container .comparison-table table {
        min-width: 600px;
    }

    .pricing-container .faq-grid {
        grid-template-columns: 1fr;
        gap: 24px;
    }

    .pricing-container .nav-menu {
        gap: 16px;
    }

    .pricing-container .nav-link {
        display: none;
    }

    .pricing-container .container {
        padding: 0 16px;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .footer-links {
        grid-template-columns: 1fr;
        gap: 30px;
    }

    .cta-content h2 {
        font-size: 2rem;
    }

    .cta-content > p {
        font-size: 1rem;
    }

    .cta-button {
        font-size: 16px;
        padding: 14px 28px;
    }

    .feature-comparison h2,
    .pricing-faq h2 {
        font-size: 2rem;
    }

    .pricing-container .faq-item {
        min-width: auto;
    }
}

@media (max-width: 480px) {
    .hero-content h1 {
        font-size: 2rem;
    }

    .pricing-container .plan-card {
        padding: 24px;
    }

    .pricing-container .plan-header {
        margin-bottom: 24px;
    }

    .pricing-container .plan-features {
        margin-bottom: 24px;
    }

    .pricing-container .price {
        font-size: 2rem;
    }

    .pricing-container .comparison-table th,
    .pricing-container .comparison-table td {
        padding: 12px 16px;
    }

    .pricing-container .faq-item {
        padding: 20px;
    }

    .billing-toggle {
        max-width: 280px;
    }

    .billing-toggle label {
        padding: 10px 16px;
        font-size: 0.875rem;
    }

    .discount-badge {
        font-size: 0.65rem;
        padding: 1px 4px;
        margin-left: 4px;
    }
}

/* Animation for pricing cards */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pricing-container .plan-card {
    animation: fadeInUp 0.6s ease-out;
}

.pricing-container .plan-card:nth-child(1) {
    animation-delay: 0.1s;
}

.pricing-container .plan-card:nth-child(2) {
    animation-delay: 0.2s;
}

.pricing-container .plan-card:nth-child(3) {
    animation-delay: 0.3s;
}

/* Smooth transitions for billing toggle */
.billing-toggle label {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Hover effects for comparison table */
.pricing-container .comparison-table tbody tr {
    transition: background-color 0.2s ease;
}

/* Focus states for accessibility */
.pricing-container .plan-button:focus,
.pricing-container .cta-button:focus,
.pricing-container .signin-btn:focus {
    outline: 2px solid #667eea;
    outline-offset: 2px;
}

.billing-toggle label:focus-within {
    outline: 2px solid #667eea;
    outline-offset: 2px;
    border-radius: 8px;
}
