const express = require("express");
const router = express.Router();
const workspaceController = require("../controllers/workspaceController");
const { isAuthenticated } = require("../middleware/auth");
const { handleTokenExpiration } = require("../middleware/googleAuth");
const {
    publicWorkspaceLimiter,
    validatePublicShareId,
    logPublicAccess,
    addSecurityHeaders,
} = require("../middleware/publicAccess");

// GET /api/workspaces - L<PERSON><PERSON>nh s<PERSON>ch workspace của user
router.get(
    "/",
    isAuthenticated,
    handleTokenExpiration,
    workspaceController.getWorkspaces
);

// GET /api/workspaces/:id - <PERSON><PERSON><PERSON> thông tin chi tiết workspace
router.get(
    "/:id",
    isAuthenticated,
    handleTokenExpiration,
    workspaceController.getWorkspaceById
);

//GET //api/workspaces/:id
router.get(
    "/:id/package",
    isAuthenticated,
    handleTokenExpiration,
    workspaceController.getWorkspacePackage
);

// POST /api/workspaces - Tạo workspace mới
router.post(
    "/",
    isAuthenticated,
    handleTokenExpiration,
    workspaceController.createWorkspace
);

// PUT /api/workspaces/:id - Cập nhật workspace
router.put(
    "/:id",
    isAuthenticated,
    handleTokenExpiration,
    workspaceController.updateWorkspace
);

// DELETE /api/workspaces/:id - Xóa workspace
router.delete(
    "/:id",
    isAuthenticated,
    handleTokenExpiration,
    workspaceController.deleteWorkspace
);

// POST /api/workspaces/generate-public-share-id - Tạo ID chia sẻ công khai
router.post(
    "/generate-public-share-id",
    isAuthenticated,
    handleTokenExpiration,
    workspaceController.generatePublicShareId
);

// PUT /api/workspaces/:id/enable-public-share - Kích hoạt chia sẻ công khai
router.put(
    "/:id/enable-public-share",
    isAuthenticated,
    handleTokenExpiration,
    workspaceController.enablePublicShare
);

// PUT /api/workspaces/:id/disable-public-share - Vô hiệu hóa chia sẻ công khai
router.put(
    "/:id/disable-public-share",
    isAuthenticated,
    handleTokenExpiration,
    workspaceController.disablePublicShare
);

// GET /api/workspaces/public/:publicShareId - Truy cập workspace public (không cần authentication)
router.get(
    "/public/:publicShareId",
    publicWorkspaceLimiter,
    addSecurityHeaders,
    validatePublicShareId,
    logPublicAccess,
    workspaceController.getPublicWorkspace
);

// GET /api/workspaces/public/:publicShareId/boards/:boardId/tasklists - Lấy tasklists của board public
router.get(
    "/public/:publicShareId/boards/:boardId/tasklists",
    publicWorkspaceLimiter,
    addSecurityHeaders,
    validatePublicShareId,
    logPublicAccess,
    workspaceController.getPublicBoardTaskLists
);

module.exports = router;
