import React, { useState } from "react";
import {
    Layout,
    Button,
    Badge,
    Progress,
    Input,
    Dropdown,
    Space,
    Divider,
    Popconfirm,
} from "antd";
import {
    PlusOutlined,
    DeleteOutlined,
    MoreOutlined,
    AppstoreOutlined,
    CrownOutlined,
} from "@ant-design/icons";
import { useNavigate, useLocation } from "react-router-dom";
import WorkspaceSelector from "./Workspace/WorkspaceSelector";
import { usePremiumFeatureContext } from "../contexts/PremiumFeatureContext";

const { Sider } = Layout;

const DashboardSidebar = ({
    collapsed,
    selectedBoard,
    boards,
    workspaces,
    selectedWorkspace,
    workspaceMembers,
    onSelectWorkspace,
    onCreateWorkspace,
    onUpdateWorkspace,
    onDeleteWorkspace,
    onManageMembers,
    leaveAssignedWorkspace,
    onSelectBoard,
    createBoard,
    onUpdateBoardName,
    onDeleteBoard,
    isMobile,
    visible,
    onClose,
    loading,
}) => {
    const [editingBoard, setEditingBoard] = useState(null);
    const navigate = useNavigate();
    const location = useLocation();

    const getBoardMenuItems = (boardId, boardName) => [
        {
            key: "delete",
            label: (
                <Popconfirm
                    title="Xác nhận xóa Board"
                    description={`Bạn có chắc chắn muốn xóa?`}
                    onConfirm={() => onDeleteBoard(boardId)}
                    okText="Xóa"
                    cancelText="Hủy"
                    okType="danger"
                    placement="bottom"
                >
                    <div className="dropdown-item delete-item">
                        <DeleteOutlined />
                        <span>Xóa</span>
                    </div>
                </Popconfirm>
            ),
        },
    ];

    const handleUpdateBoardName = (boardId, newName) => {
        if (onUpdateBoardName) {
            onUpdateBoardName(boardId, newName);
        }
        setEditingBoard(null);
    };

    const handleSelectBoard = (boardId) => {
        onSelectBoard(boardId);
        // Navigate to dashboard when selecting board
        navigate("/app/dashboard");
        // Close mobile sidebar when selecting board
        if (isMobile && onClose) {
            onClose();
        }
    };

    const { handleFeatureClick } = usePremiumFeatureContext();

    const handleCreateBoard = () => {
        const canProceed = handleFeatureClick("board", boards.length);
        if (canProceed) {
            if (!selectedWorkspace) {
                return;
            }
            createBoard(selectedWorkspace);
        }
    };

    const handleManageMembers = () => {
        const currentWorkspace = workspaces.find(
            (ws) => ws.id === selectedWorkspace
        );
        const canProceed = handleFeatureClick(
            "member",
            workspaceMembers?.length || 0,
            currentWorkspace
        );
        if (canProceed) {
            onManageMembers();
        }
    };

    // Boards are already filtered by workspace from the API
    const workspaceBoards = boards;

    // check if the user is the owner of the workspace
    const currentWorkspace = workspaces.find(
        (ws) => ws.id === selectedWorkspace
    );
    const isUserWorkspaceOwner = currentWorkspace?.isOwned || false;

    const handleTaskAssignClick = () => {
        navigate("/app/task-assign");
        // Close mobile sidebar when selecting
        if (isMobile && onClose) {
            onClose();
        }
    };

    return (
        <Sider
            trigger={null}
            collapsible
            collapsed={collapsed}
            className="dashboard-sidebar"
            width={280}
        >
            <div className="sidebar-content">
                {!collapsed && (
                    <>
                        <div style={{ flex: 1 }}>
                            {/* Workspace Selector */}
                            <div className="sidebar-section workspace-section">
                                <WorkspaceSelector
                                    workspaces={workspaces}
                                    selectedWorkspace={selectedWorkspace}
                                    onSelectWorkspace={onSelectWorkspace}
                                    onCreateWorkspace={onCreateWorkspace}
                                    onUpdateWorkspace={onUpdateWorkspace}
                                    onDeleteWorkspace={onDeleteWorkspace}
                                    onManageMembers={onManageMembers}
                                    leaveAssignedWorkspace={
                                        leaveAssignedWorkspace
                                    }
                                    loading={loading}
                                />
                            </div>

                            <Divider style={{ margin: "12px 0" }} />

                            {/* Your tasks */}
                            <div className="sidebar-section">
                                <div className="section-header">
                                    <h3>Các task được giao</h3>
                                </div>
                                <div className="section-content">
                                    <div className="board-menu-item">
                                        <div
                                            className={`board-item-content ${
                                                location.pathname ===
                                                "/app/task-assign"
                                                    ? "selected"
                                                    : ""
                                            }`}
                                            onClick={handleTaskAssignClick}
                                            style={{ cursor: "pointer" }}
                                        >
                                            <div className="list-info">
                                                <div
                                                    className="list-color"
                                                    style={{
                                                        backgroundColor:
                                                            "#1890ff",
                                                    }}
                                                ></div>
                                                <span className="list-name">
                                                    Task được giao
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <Divider style={{ margin: "12px 0" }} />

                            {/* Boards Section */}
                            <div className="sidebar-section">
                                <div className="section-header">
                                    <h3>Các board của bạn</h3>
                                    {isUserWorkspaceOwner && (
                                        <Button
                                            type="text"
                                            icon={<PlusOutlined />}
                                            size="small"
                                            onClick={handleCreateBoard}
                                            disabled={!selectedWorkspace}
                                            title={
                                                !selectedWorkspace
                                                    ? "Vui lòng chọn workspace trước"
                                                    : "Tạo board mới"
                                            }
                                        />
                                    )}
                                </div>

                                <div className="boards-container">
                                    {workspaceBoards.length === 0 &&
                                    selectedWorkspace ? (
                                        isUserWorkspaceOwner ? (
                                            <div className="empty-boards">
                                                <p>
                                                    Chưa có board nào trong
                                                    workspace
                                                </p>
                                                <Button
                                                    type="dashed"
                                                    icon={<PlusOutlined />}
                                                    onClick={handleCreateBoard}
                                                    block
                                                >
                                                    Tạo board đầu tiên
                                                </Button>
                                            </div>
                                        ) : (
                                            <div className="empty-boards">
                                                <p>
                                                    Bạn chưa được thêm vào board
                                                    nào
                                                </p>
                                            </div>
                                        )
                                    ) : (
                                        workspaceBoards.map((board) => (
                                            <div
                                                key={board.id}
                                                className="board-menu-item"
                                            >
                                                <div
                                                    className={`board-item-content ${
                                                        selectedBoard ===
                                                            board.id &&
                                                        location.pathname ===
                                                            "/app/dashboard"
                                                            ? "selected"
                                                            : ""
                                                    }`}
                                                    onClick={() =>
                                                        handleSelectBoard(
                                                            board.id
                                                        )
                                                    }
                                                >
                                                    <div className="list-info">
                                                        <div
                                                            className="list-color"
                                                            style={{
                                                                backgroundColor:
                                                                    board.isGoogleSynced
                                                                        ? "#4285f4"
                                                                        : "#1890ff",
                                                            }}
                                                        ></div>
                                                        {editingBoard ===
                                                            board.id &&
                                                        isUserWorkspaceOwner ? (
                                                            <Input
                                                                defaultValue={
                                                                    board.name
                                                                }
                                                                className="board-name-input"
                                                                size="small"
                                                                onClick={(e) =>
                                                                    e.stopPropagation()
                                                                }
                                                                onPressEnter={(
                                                                    e
                                                                ) =>
                                                                    handleUpdateBoardName(
                                                                        board.id,
                                                                        e.target
                                                                            .value
                                                                    )
                                                                }
                                                                onBlur={(e) =>
                                                                    handleUpdateBoardName(
                                                                        board.id,
                                                                        e.target
                                                                            .value
                                                                    )
                                                                }
                                                                autoFocus
                                                            />
                                                        ) : (
                                                            <span
                                                                className="list-name editable"
                                                                onDoubleClick={(
                                                                    e
                                                                ) => {
                                                                    e.stopPropagation();
                                                                    setEditingBoard(
                                                                        board.id
                                                                    );
                                                                }}
                                                            >
                                                                {board.name}
                                                            </span>
                                                        )}
                                                    </div>
                                                </div>
                                                {/* Hiển thị nút xóa nếu không phải board mặc định */}
                                                {board.isDefault ? null : (
                                                    <Dropdown
                                                        menu={{
                                                            items: getBoardMenuItems(
                                                                board.id,
                                                                board.name
                                                            ),
                                                        }}
                                                        trigger={["click"]}
                                                        placement="bottomRight"
                                                    >
                                                        <Button
                                                            type="text"
                                                            icon={
                                                                <MoreOutlined />
                                                            }
                                                            size="small"
                                                            className="board-menu-btn"
                                                            onClick={(e) =>
                                                                e.stopPropagation()
                                                            }
                                                        />
                                                    </Dropdown>
                                                )}
                                            </div>
                                        ))
                                    )}
                                </div>
                            </div>
                        </div>

                        {/* Premium Button */}
                        <div className="sidebar-section premium-section">
                            <Button
                                type="primary"
                                icon={<CrownOutlined />}
                                block
                                onClick={() => navigate("/pricing")}
                                style={{
                                    background:
                                        "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                                    border: "none",
                                    borderRadius: "8px",
                                    height: "40px",
                                    fontWeight: "500",
                                }}
                            >
                                Nâng cấp Premium
                            </Button>
                        </div>
                    </>
                )}
            </div>
        </Sider>
    );
};

export default DashboardSidebar;
