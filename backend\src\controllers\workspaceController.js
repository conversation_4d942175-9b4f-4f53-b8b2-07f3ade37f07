const workspaceService = require("../services/workspaceService");

const createWorkspace = async (req, res) => {
    try {
        const { name, description } = req.body;

        const workspace = await workspaceService.createWorkspace(
            name,
            description,
            req.user.id
        );

        res.status(201).json(workspace);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
};

const getWorkspaces = async (req, res) => {
    try {
        const workspaces = await workspaceService.getWorkspacesByOwner(
            req.user.id
        );
        res.status(200).json(workspaces);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
};

const getWorkspaceById = async (req, res) => {
    try {
        const { id } = req.params;

        const workspace = await workspaceService.getWorkspaceById(
            id,
            req.user.id
        );

        res.status(200).json(workspace);
    } catch (error) {
        if (
            error.message.includes("không tồn tại") ||
            error.message.includes("không có quyền")
        ) {
            return res.status(404).json({ message: error.message });
        }
        res.status(500).json({ message: error.message });
    }
};

const getWorkspacePackage = async (req, res) => {
    try {
        const { id } = req.params;
        const packageId = await workspaceService.getWorkspacePackage(id);
        res.status(200).json({ packageId });
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
};

const updateWorkspace = async (req, res) => {
    try {
        const { id } = req.params;
        const { name, description } = req.body;

        const updateData = {};
        if (name !== undefined) updateData.name = name;
        if (description !== undefined) updateData.description = description;

        const workspace = await workspaceService.updateWorkspace(
            id,
            updateData,
            req.user.id
        );

        res.status(200).json(workspace);
    } catch (error) {
        if (
            error.message.includes("không tồn tại") ||
            error.message.includes("không có quyền")
        ) {
            return res.status(404).json({ message: error.message });
        }
        res.status(500).json({ message: error.message });
    }
};

const deleteWorkspace = async (req, res) => {
    try {
        const { id } = req.params;

        const result = await workspaceService.deleteWorkspace(id, req.user.id);

        res.status(200).json(result);
    } catch (error) {
        if (
            error.message.includes("không tồn tại") ||
            error.message.includes("không có quyền")
        ) {
            return res.status(404).json({ message: error.message });
        }
        if (error.message.includes("Không thể xóa workspace có chứa board")) {
            return res.status(400).json({ message: error.message });
        }
        res.status(500).json({ message: error.message });
    }
};

const generatePublicShareId = async (req, res) => {
    try {
        const publicShareId = await workspaceService.generatePublicShareId();
        res.status(200).json({ publicShareId });
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
};

const enablePublicShare = async (req, res) => {
    try {
        const { id } = req.params;
        const workspace = await workspaceService.enablePublicShare(id);
        res.status(200).json(workspace);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
};

const disablePublicShare = async (req, res) => {
    try {
        const { id } = req.params;
        const workspace = await workspaceService.disablePublicShare(id);
        res.status(200).json(workspace);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
};

const getPublicWorkspace = async (req, res) => {
    try {
        const { publicShareId } = req.params;
        const workspace = await workspaceService.getPublicWorkspace(
            publicShareId
        );
        res.status(200).json(workspace);
    } catch (error) {
        if (
            error.message.includes("không tồn tại") ||
            error.message.includes("không được chia sẻ")
        ) {
            return res.status(404).json({ message: error.message });
        }
        res.status(500).json({ message: error.message });
    }
};

const getPublicBoardTaskLists = async (req, res) => {
    try {
        const { publicShareId, boardId } = req.params;
        const taskLists = await workspaceService.getPublicBoardTaskLists(
            publicShareId,
            boardId
        );
        res.status(200).json(taskLists);
    } catch (error) {
        if (
            error.message.includes("không tồn tại") ||
            error.message.includes("không được chia sẻ") ||
            error.message.includes("không thuộc về")
        ) {
            return res.status(404).json({ message: error.message });
        }
        res.status(500).json({ message: error.message });
    }
};

module.exports = {
    createWorkspace,
    getWorkspaces,
    getWorkspaceById,
    getWorkspacePackage,
    updateWorkspace,
    deleteWorkspace,
    generatePublicShareId,
    enablePublicShare,
    disablePublicShare,
    getPublicWorkspace,
    getPublicBoardTaskLists,
};
