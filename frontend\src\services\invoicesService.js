import axios from "../utils/axiosCustomize";

const createInvoice = async (invoiceData) => {
    const response = await axios.post(`/invoices`, invoiceData);
    return response.data;
};

const createInvoiceTrial = async (invoiceData) => {
    const response = await axios.post(`/invoices/trial`, invoiceData);
    return response.data;
};

const getUserInvoices = async () => {
    const response = await axios.get(`/invoices/user`);
    return response.data;
};

const getWorkspaceInvoices = async (workspaceId) => {
    const response = await axios.get(`/invoices/workspace/${workspaceId}`);
    return response.data;
};

export default {
    createInvoice,
    getUserInvoices,
    getWorkspaceInvoices,
    createInvoiceTrial,
};
