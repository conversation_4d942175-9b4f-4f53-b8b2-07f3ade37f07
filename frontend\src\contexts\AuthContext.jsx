import React, {
    createContext,
    useContext,
    useState,
    useEffect,
    useCallback,
} from "react";
import { getToken, setToken, removeToken } from "../utils/tokenManager";
import axios from "../utils/axiosCustomize";
import { useNavigate, useLocation } from "react-router-dom";
import { useMessage } from "./MessageContext";
import userService from "../services/userService";

const AuthContext = createContext(null);

export const AuthProvider = ({ children }) => {
    const [user, setUser] = useState(null);
    const [loading, setLoading] = useState(true);
    const navigate = useNavigate();
    const location = useLocation();
    const { success, error: showError } = useMessage();
    const [isTrial, setIsTrial] = useState(false);

    const login = async () => {
        try {
            setLoading(true);

            // Check current token before login
            const existingToken = getToken();
            if (existingToken) {
                try {
                    const response = await axios.get("/auth/profile");
                    if (response.data.status === "success") {
                        // Token is valid, redirect directly to dashboard
                        // console.log('Token is valid, redirecting to dashboard');
                        navigate("/app/dashboard");
                        return;
                    }
                } catch (error) {
                    // Token is invalid, continue with Google login
                    // console.log(
                    //     'Token is invalid, continuing with Google login'
                    // );
                }
            }

            // Redirect to Google OAuth if no token or token is invalid
            window.location.href = `${
                import.meta.env.VITE_API_URL
            }/auth/google`;
        } catch (error) {
            const errorMessage =
                error.response?.data?.error || "Đăng nhập thất bại";
            showError(errorMessage);
            setLoading(false);
        }
    };

    const logout = async () => {
        try {
            await axios.get("/auth/logout");
        } catch (error) {
            console.error("Lỗi khi đăng xuất:", error);
        } finally {
            removeToken();
            setUser(null);
            setLoading(false);
            navigate("/");
        }
    };

    const checkUserTrial = useCallback(async () => {
        try {
            // Only check trial if user has a token
            const token = getToken();
            if (!token) {
                setIsTrial(false);
                return;
            }

            const response = await userService.getCurrentUser();
            console.log(response.isTrial);
            setIsTrial(response.isTrial);
        } catch (error) {
            console.error("Error checking user trial:", error);
            setIsTrial(false);
        }
    }, []);

    const fetchUserProfile = useCallback(async () => {
        try {
            const response = await axios.get("/auth/profile");
            if (response.data.status === "success") {
                setUser({
                    name: response.data.data.fullName || "Người dùng",
                    email: response.data.data.email || "",
                    photoUrl:
                        response.data.data.photoUrl ||
                        "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face&auto=format",
                });
            }
        } catch (error) {
            console.error("Lỗi khi lấy thông tin người dùng:", error);
        }
    }, []);

    // Handle OAuth callback and initialize user
    useEffect(() => {
        const handleOAuthCallback = async () => {
            // Handle query parameters from OAuth callback
            const urlParams = new URLSearchParams(location.search);
            const token = urlParams.get("token");
            const error = urlParams.get("error");

            if (token) {
                // Save token to localStorage
                setToken(token);
                success("Đăng nhập thành công!");

                // Remove query parameters from URL
                window.history.replaceState(
                    {},
                    document.title,
                    window.location.pathname
                );

                // Fetch user profile and navigate to dashboard
                await fetchUserProfile();
                await checkUserTrial();
                navigate("/app/dashboard");
            } else if (error) {
                let errorMessage = "Đăng nhập thất bại";
                if (error === "login_failed") {
                    errorMessage =
                        "Đăng nhập không thành công. Vui lòng thử lại.";
                } else if (error === "callback_error") {
                    errorMessage =
                        "Đã xảy ra lỗi trong quá trình xử lý. Vui lòng thử lại.";
                }
                showError(errorMessage);

                // Remove query parameters from URL
                window.history.replaceState(
                    {},
                    document.title,
                    window.location.pathname
                );
            } else {
                // No OAuth callback, check existing token
                const existingToken = getToken();
                if (existingToken) {
                    await fetchUserProfile();
                    await checkUserTrial();
                }
            }

            setLoading(false);
        };

        handleOAuthCallback();
    }, [location.search]); // Simplified dependencies - only location.search is needed

    const value = {
        user,
        loading,
        isAuthenticated: !!user,
        isTrial,
        login,
        logout,
        fetchUserProfile,
    };

    return (
        <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
    );
};

export const useAuth = () => {
    const context = useContext(AuthContext);
    if (context === undefined) {
        throw new Error("useAuth must be used within an AuthProvider");
    }
    return context;
};
