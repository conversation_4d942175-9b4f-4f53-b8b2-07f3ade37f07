// <PERSON><PERSON><PERSON> nghĩa c<PERSON>u hình package
export const PACKAGE_CONFIGS = {
  1: {
    name: "Free",
    limitMember: 1,
    limitBoard: 5,
    labelFeature: 0,
    teamFeature: 0,
    description: "<PERSON><PERSON><PERSON> c<PERSON> bản cho cá nhân"
  },
  2: {
    name: "Pro",
    limitMember: 1,
    limitBoard: 0, // unlimited
    labelFeature: 1,
    teamFeature: 0,
    description: "<PERSON><PERSON><PERSON> nâng cao cho cá nhân"
  },
  3: {
    name: "Team",
    limitMember: 20,
    limitBoard: 0, // unlimited
    labelFeature: 1,
    teamFeature: 1,
    description: "Gói dành cho team"
  }
};

// Service để kiểm tra quyền truy cập tính năng
export const checkFeatureAccess = (workspace, feature) => {
  if (!workspace || !workspace.packageId) {
    return { hasAccess: false, reason: "Workspace không hợp lệ" };
  }

  const packageConfig = PACKAGE_CONFIGS[workspace.packageId];
  if (!packageConfig) {
    return { hasAccess: false, reason: "Package không hợp lệ" };
  }

  switch (feature) {
    case 'member':
      return { 
        hasAccess: packageConfig.teamFeature === 1,
        limit: packageConfig.limitMember,
        reason: packageConfig.teamFeature === 0 ? "Quản lý thành viên chỉ có trong gói Team" : null
      };
    
    case 'board':
      return { 
        hasAccess: true, 
        limit: packageConfig.limitBoard,
        reason: null 
      };
    
    case 'label':
      return { 
        hasAccess: packageConfig.labelFeature === 1,
        reason: packageConfig.labelFeature === 0 ? "Tính năng Label chỉ có trong gói Pro trở lên" : null
      };
    
    case 'team':
      return { 
        hasAccess: packageConfig.teamFeature === 1,
        reason: packageConfig.teamFeature === 0 ? "Tính năng Team chỉ có trong gói Team" : null
      };
    
    default:
      return { hasAccess: false, reason: "Tính năng không xác định" };
  }
};

// Kiểm tra giới hạn số lượng
export const checkLimit = (workspace, feature, currentCount) => {
  const access = checkFeatureAccess(workspace, feature);
  
  if (!access.hasAccess) {
    return { canProceed: false, reason: access.reason };
  }

  if (access.limit === 0) {
    return { canProceed: true, reason: null }; // Unlimited
  }

  if (currentCount >= access.limit) {
    return { 
      canProceed: false, 
      reason: `Đã đạt giới hạn ${access.limit} ${feature === 'member' ? 'thành viên' : 'board'} trong gói ${PACKAGE_CONFIGS[workspace.packageId].name}` 
    };
  }

  return { canProceed: true, reason: null };
}; 