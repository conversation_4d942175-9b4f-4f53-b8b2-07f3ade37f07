import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { useAuth } from "../../contexts/AuthContext";
import ModalPayment from "./ModalPayment";
import invoiceService from "../../services/invoicesService";
import workspaceService from "../../services/workspaceService";
import "./Pricing.css";

const Pricing = () => {
    const navigate = useNavigate();
    const { isAuthenticated, login, isTrial } = useAuth();
    const [billingCycle, setBillingCycle] = useState("monthly"); // 'monthly' | 'yearly'
    const [paymentModalVisible, setPaymentModalVisible] = useState(false);
    const [selectedPlan, setSelectedPlan] = useState(null);
    const [workspacePackage, setWorkspacePackage] = useState(null);

    const workspaceId = localStorage.getItem("selectedWorkspace");

    useEffect(() => {
        getWorkspacePackage();
    }, []);

    const getWorkspacePackage = async () => {
        if (!workspaceId) return;
        const packageId = await workspaceService.getWorkspacePackage(
            workspaceId
        );
        setWorkspacePackage(packageId);
    };

    const handleLogin = () => {
        login();
    };

    const handleGoHome = () => {
        navigate("/");
    };

    const handleGoToDashboard = () => {
        navigate("/app/dashboard");
    };

    const handleSelectPlan = (plan) => {
        if (!isAuthenticated) {
            login();
            return;
        }

        if (!isTrial) {
            const packageId = pricingPlans[plan.name.toLowerCase()].id;
            handleRegisTrial(workspaceId, packageId);
            navigate("/app/dashboard");
            return;
        }

        if (plan.name === "Free") {
            // Gói free không cần thanh toán, chuyển thẳng về dashboard
            handleGoToDashboard();
            return;
        }

        // Mở modal thanh toán cho gói trả phí
        setSelectedPlan(plan);
        setPaymentModalVisible(true);
    };

    const handleClosePaymentModal = () => {
        setPaymentModalVisible(false);
        setSelectedPlan(null);
    };

    const handleRegisTrial = async (workspaceId, packageId) => {
        try {
            const invoiceData = {
                workspaceId,
                packageId,
            };
            invoiceService.createInvoiceTrial(invoiceData);
        } catch (error) {
            throw new Error(`Lỗi đăng ký trial: ${error.message}`);
        }
    };

    // Pricing data based on packageService.js
    const pricingPlans = {
        free: {
            name: "free",
            description: "Gói cơ bản cho cá nhân",
            monthlyPrice: 0,
            yearlyPrice: 0,
            popular: false,
            features: [
                "1 người dùng",
                "5 boards",
                "Unlimited tasks",
                "Giao diện Kanban cơ bản",
                "Đồng bộ với Google Tasks",
                "Hỗ trợ qua email",
            ],
            limitations: [],
        },
        pro: {
            name: "pro",
            description: "Gói nâng cao cho cá nhân",
            monthlyPrice: 5000,
            yearlyPrice: 10000,
            popular: true,
            features: [
                "1 người dùng",
                "Unlimited boards",
                "Unlimited tasks",
                "Hệ thống Label màu sắc",
                "Xuất file Google Sheets",
                "Đính kèm Google Drive",
            ],
            limitations: [],
        },
        team: {
            name: "team",
            description: "Gói dành cho team",
            monthlyPrice: 5000,
            yearlyPrice: 10000,
            popular: false,
            features: [
                "Lên đến 20 thành viên",
                "Unlimited boards",
                "Unlimited tasks",
                "Tất cả tính năng Pro",
                "Quản lý thành viên",
                "Chia sẻ workspace real-time",
            ],
            limitations: [],
        },
    };

    const getPrice = (plan) => {
        if (plan.name === "Free") return "Miễn phí";
        const price =
            billingCycle === "yearly" ? plan.yearlyPrice : plan.monthlyPrice;
        const period = billingCycle === "yearly" ? "năm" : "tháng";
        return `${price} VND/${period}`;
    };

    const getSavings = (plan) => {
        if (plan.name === "Free" || billingCycle === "monthly") return null;
        const monthlyCost = plan.monthlyPrice * 12;
        const savings = Math.round(
            ((monthlyCost - plan.yearlyPrice) / monthlyCost) * 100
        );
        return `Tiết kiệm ${savings}%`;
    };

    const commonFeatures = [
        "Giao diện Kanban trực quan",
        "Đồng bộ với Google Tasks",
        "Truy cập trên mọi thiết bị",
        "Bảo mật cao",
        "Cập nhật tự động",
    ];

    const faqs = [
        {
            question: "Tôi có thể thay đổi gói dịch vụ không?",
            answer: "Có, bạn có thể nâng cấp hoặc hạ cấp gói dịch vụ bất kỳ lúc nào. Thay đổi sẽ có hiệu lực ngay lập tức.",
        },
        {
            question: "Có miễn phí dùng thử không?",
            answer: "Gói Free hoàn toàn miễn phí và không giới hạn thời gian. Bạn có thể nâng cấp lên Pro hoặc Team khi cần thêm tính năng.",
        },
        {
            question: "Thanh toán như thế nào?",
            answer: "Chúng tôi hỗ trợ thanh toán qua thẻ tín dụng, PayPal và chuyển khoản ngân hàng. Tất cả đều được bảo mật.",
        },
        {
            question: "Dữ liệu có an toàn không?",
            answer: "Dữ liệu của bạn được mã hóa và lưu trữ an toàn. Chúng tôi tuân thủ các tiêu chuẩn bảo mật quốc tế.",
        },
        {
            question: "Có hỗ trợ khách hàng không?",
            answer: "Có, chúng tôi cung cấp hỗ trợ qua email cho tất cả người dùng và hỗ trợ 24/7 cho gói Team.",
        },
    ];

    return (
        <div className="pricing-container">
            {/* Header */}
            <header className="pricing-header">
                <div className="container">
                    <div className="header-content">
                        <div className="logo" onClick={handleGoHome}>
                            <img
                                src={"/gwtask.png"}
                                style={{ width: "24px", height: "24px" }}
                                alt="GW Tasks"
                            />
                            <span className="logo-text">GW Tasks</span>
                        </div>

                        <nav className="nav-menu">
                            <a href="/" className="nav-link">
                                Trang chủ
                            </a>
                            <a href="/features" className="nav-link">
                                Tính năng
                            </a>
                            {!isAuthenticated ? (
                                <button
                                    onClick={handleLogin}
                                    className="signin-btn"
                                >
                                    Đăng nhập
                                </button>
                            ) : (
                                <button
                                    onClick={handleGoToDashboard}
                                    className="signin-btn"
                                >
                                    Dashboard
                                </button>
                            )}
                        </nav>
                    </div>
                </div>
            </header>

            {/* Hero Section */}
            <section className="pricing-hero">
                <div className="container">
                    <div className="hero-content">
                        <h1>Bảng giá đơn giản và minh bạch</h1>
                        <p>
                            Chọn gói phù hợp với nhu cầu của bạn. Bắt đầu miễn
                            phí, nâng cấp khi cần.
                        </p>

                        {/* Billing Toggle */}
                        <div className="billing-toggle">
                            <label
                                className={
                                    billingCycle === "monthly" ? "active" : ""
                                }
                            >
                                <input
                                    type="radio"
                                    name="billing"
                                    value="monthly"
                                    checked={billingCycle === "monthly"}
                                    onChange={(e) =>
                                        setBillingCycle(e.target.value)
                                    }
                                />
                                Hàng tháng
                            </label>
                            <label
                                className={
                                    billingCycle === "yearly" ? "active" : ""
                                }
                            >
                                <input
                                    type="radio"
                                    name="billing"
                                    value="yearly"
                                    checked={billingCycle === "yearly"}
                                    onChange={(e) =>
                                        setBillingCycle(e.target.value)
                                    }
                                />
                                Hàng năm
                                <span className="discount-badge">
                                    Tiết kiệm 20%
                                </span>
                            </label>
                        </div>
                    </div>
                </div>
            </section>

            {/* Pricing Cards */}
            <section className="pricing-plans">
                <div className="container">
                    <div className="plans-grid">
                        {Object.values(pricingPlans).map((plan, index) => {
                            const savings = getSavings(plan);
                            return (
                                <div
                                    key={index}
                                    className={`plan-card ${
                                        plan.popular ? "popular" : ""
                                    }`}
                                >
                                    {plan.popular && (
                                        <div className="popular-badge">
                                            Phổ biến nhất
                                        </div>
                                    )}

                                    <div className="plan-header">
                                        <h3>{plan.name}</h3>
                                        <p className="plan-description">
                                            {plan.description}
                                        </p>
                                        <div className="plan-price">
                                            <span className="price">
                                                {getPrice(plan)}
                                            </span>
                                            {savings && (
                                                <span className="savings">
                                                    {savings}
                                                </span>
                                            )}
                                        </div>
                                    </div>

                                    <div className="plan-features">
                                        <ul>
                                            {plan.features.map(
                                                (feature, fIndex) => (
                                                    <li
                                                        key={fIndex}
                                                        className="feature-included"
                                                    >
                                                        <svg
                                                            width="16"
                                                            height="16"
                                                            viewBox="0 0 24 24"
                                                            fill="none"
                                                        >
                                                            <path
                                                                d="M20 6L9 17l-5-5"
                                                                stroke="#34a853"
                                                                strokeWidth="2"
                                                                strokeLinecap="round"
                                                                strokeLinejoin="round"
                                                            />
                                                        </svg>
                                                        {feature}
                                                    </li>
                                                )
                                            )}
                                            {plan.limitations.map(
                                                (limitation, lIndex) => (
                                                    <li
                                                        key={lIndex}
                                                        className="feature-excluded"
                                                    >
                                                        <svg
                                                            width="16"
                                                            height="16"
                                                            viewBox="0 0 24 24"
                                                            fill="none"
                                                        >
                                                            <path
                                                                d="M18 6L6 18M6 6l12 12"
                                                                stroke="#ea4335"
                                                                strokeWidth="2"
                                                                strokeLinecap="round"
                                                                strokeLinejoin="round"
                                                            />
                                                        </svg>
                                                        {limitation}
                                                    </li>
                                                )
                                            )}
                                        </ul>
                                    </div>

                                    <div className="plan-action">
                                        <button
                                            className={`plan-button ${
                                                plan.popular
                                                    ? "primary"
                                                    : "secondary"
                                            }`}
                                            onClick={() =>
                                                handleSelectPlan(plan)
                                            }
                                        >
                                            {/* Nếu người dùng chưa dùng thử thì hiển thị "Dùng thử 3 ngày", ngược lại hiển thị "Nâng cấp ngay", nếu người dùng đang sử dụng gói nào đó thì hiển thị "Đang sử dụng" */}
                                            {isAuthenticated && !isTrial && workspacePackage === plan.name
                                                ? "Đang sử dụng"
                                                : isAuthenticated && !isTrial
                                                ? "Dùng thử 3 ngày"
                                                : "Nâng cấp ngay"}
                                        </button>
                                    </div>
                                </div>
                            );
                        })}
                    </div>
                </div>
            </section>

            {/* Feature Comparison Table */}
            <section className="feature-comparison">
                <div className="container">
                    <h2>So sánh chi tiết các gói</h2>
                    <div className="comparison-table">
                        <table>
                            <thead>
                                <tr>
                                    <th>Tính năng</th>
                                    <th>Free</th>
                                    <th>Pro</th>
                                    <th>Team</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>Số người dùng</td>
                                    <td>1</td>
                                    <td>1</td>
                                    <td>Lên đến 20</td>
                                </tr>
                                <tr>
                                    <td>Số boards</td>
                                    <td>5</td>
                                    <td>Không giới hạn</td>
                                    <td>Không giới hạn</td>
                                </tr>
                                <tr>
                                    <td>Tasks</td>
                                    <td>Không giới hạn</td>
                                    <td>Không giới hạn</td>
                                    <td>Không giới hạn</td>
                                </tr>
                                <tr>
                                    <td>Giao diện Kanban</td>
                                    <td>✓</td>
                                    <td>✓</td>
                                    <td>✓</td>
                                </tr>
                                <tr>
                                    <td>Đồng bộ Google Tasks</td>
                                    <td>✓</td>
                                    <td>✓</td>
                                    <td>✓</td>
                                </tr>
                                <tr>
                                    <td>Hệ thống Label</td>
                                    <td>✗</td>
                                    <td>✓</td>
                                    <td>✓</td>
                                </tr>
                                <tr>
                                    <td>Xuất Google Sheets</td>
                                    <td>✗</td>
                                    <td>✓</td>
                                    <td>✓</td>
                                </tr>
                                <tr>
                                    <td>Đính kèm Google Drive</td>
                                    <td>✗</td>
                                    <td>✓</td>
                                    <td>✓</td>
                                </tr>
                                <tr>
                                    <td>Quản lý team</td>
                                    <td>✗</td>
                                    <td>✗</td>
                                    <td>✓</td>
                                </tr>
                                <tr>
                                    <td>Chia sẻ real-time</td>
                                    <td>✗</td>
                                    <td>✗</td>
                                    <td>✓</td>
                                </tr>
                                <tr>
                                    <td>Hỗ trợ khách hàng</td>
                                    <td>Email</td>
                                    <td>Ưu tiên</td>
                                    <td>24/7</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </section>

            {/* FAQ Section */}
            <section className="pricing-faq">
                <div className="container">
                    <h2>Câu hỏi thường gặp</h2>
                    <div className="faq-grid">
                        {faqs.map((faq, index) => (
                            <div key={index} className="faq-item">
                                <h3>{faq.question}</h3>
                                <p>{faq.answer}</p>
                            </div>
                        ))}
                    </div>
                </div>
            </section>

            {/* CTA Section */}
            <section className="pricing-cta">
                <div className="container">
                    <div className="cta-content">
                        <h2>Sẵn sàng bắt đầu?</h2>
                        <p>
                            Tham gia cùng hàng nghìn người dùng đang quản lý
                            công việc hiệu quả hơn với GW Tasks
                        </p>
                        <div className="cta-buttons">
                            {!isAuthenticated ? (
                                <button
                                    className="cta-button primary"
                                    onClick={handleLogin}
                                >
                                    <svg
                                        width="20"
                                        height="20"
                                        viewBox="0 0 24 24"
                                        fill="none"
                                    >
                                        <path
                                            fill="#4285F4"
                                            d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                                        />
                                        <path
                                            fill="#34A853"
                                            d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                                        />
                                        <path
                                            fill="#FBBC05"
                                            d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                                        />
                                        <path
                                            fill="#EA4335"
                                            d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                                        />
                                    </svg>
                                    Bắt đầu miễn phí
                                </button>
                            ) : (
                                <button
                                    className="cta-button primary"
                                    onClick={handleGoToDashboard}
                                >
                                    <svg
                                        width="20"
                                        height="20"
                                        viewBox="0 0 24 24"
                                        fill="none"
                                        stroke="currentColor"
                                        strokeWidth="2"
                                    >
                                        <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z" />
                                        <polyline points="9,22 9,12 15,12 15,22" />
                                    </svg>
                                    Vào Dashboard
                                </button>
                            )}
                        </div>
                        <p className="cta-note">
                            Không cần thẻ tín dụng • Hủy bất kỳ lúc nào
                        </p>
                    </div>
                </div>
            </section>

            {/* Footer */}
            <footer className="pricing-footer">
                <div className="container">
                    <div className="footer-content">
                        <div className="footer-brand">
                            <div className="logo">
                                <img
                                    src={"/gwtask.png"}
                                    style={{ width: "24px", height: "24px" }}
                                    alt="GW Tasks"
                                />
                                <span className="logo-text">GW Tasks</span>
                            </div>
                            <p>Ứng dụng desktop cho Google Tasks</p>
                        </div>
                        <div className="footer-links">
                            <div className="footer-column">
                                <h4>Sản phẩm</h4>
                                <a href="/">Trang chủ</a>
                                <a href="/features">Tính năng</a>
                                <a href="/pricing">Bảng giá</a>
                            </div>
                            <div className="footer-column">
                                <h4>Hỗ trợ</h4>
                                <a href="#help">Trợ giúp</a>
                                <a href="#contact">Liên hệ</a>
                                <a href="#privacy">Chính sách bảo mật</a>
                            </div>
                        </div>
                    </div>
                    <div className="footer-bottom">
                        <p>&copy; 2024 GW Tasks. Không liên kết với Google.</p>
                    </div>
                </div>
            </footer>

            {/* Payment Modal */}
            <ModalPayment
                visible={paymentModalVisible}
                onClose={handleClosePaymentModal}
                selectedPlan={selectedPlan}
                billingCycle={billingCycle}
                workspaceId
            />
        </div>
    );
};

export default Pricing;
