/*pending   → tạ<PERSON> h<PERSON><PERSON> đơn, chưa thanh toán
paid      → thanh toán xong, đang còn hạn
expired   → thanh toán xong nhưng đã hết hạn
cancelled → bị hủy */

module.exports = (sequelize, DataTypes) => {
    const Invoices = sequelize.define(
        "Invoices",
        {
            id: {
                type: DataTypes.INTEGER,
                primaryKey: true,
                autoIncrement: true,
            },
            userId: {
                type: DataTypes.INTEGER,
                allowNull: false,
            },
            workspaceId: {
                type: DataTypes.INTEGER,
                allowNull: false,
            },
            amount: {
                type: DataTypes.FLOAT,
                allowNull: false,
            },

            status: {
                type: DataTypes.ENUM("pending", "paid", "expired", "cancelled"),
                allowNull: false,
            },
            packageId: {
                type: DataTypes.INTEGER,
                allowNull: false,
            },
            packageDuration: {
                type: DataTypes.INTEGER,
                allowNull: true,
            },
            packageStartDate: {
                type: DataTypes.DATE,
                allowNull: false,
            },
            packageEndDate: {
                type: DataTypes.DATE,
                allowNull: false,
            },
        },
        {
            tableName: "invoices",
            timestamps: true,
        }
    );

    Invoices.associate = (models) => {
        Invoices.belongsTo(models.User, { foreignKey: "userId" });
        Invoices.belongsTo(models.Workspace, { foreignKey: "workspaceId" });
    };

    return Invoices;
};
