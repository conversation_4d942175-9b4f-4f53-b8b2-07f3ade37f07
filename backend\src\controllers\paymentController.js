const paymentService = require("../services/paymentService");

const createPayment = async (req, res) => {
    try {
        const { workspaceId, packageId, packageDuration, amount } = req.body;
        const userId = req.user.id;

        const payment = await paymentService.createPayment(
            userId,
            workspaceId,
            amount,
            packageId,
            packageDuration
        );

        res.status(201).json({
            status: "success",
            message: "Tạo thanh toán thành công",
            data: payment,
        });
    } catch (error) {
        console.error(error.response?.data || error.message);
        res.status(500).json({ error: "Tạo mã VietQR thất bại" });
    }
};

const handlePaymentCallback = async (req, res) => {
    try {
        const data = req.body;
        const result = await paymentService.handlePaymentCallback(
            data.invoiceId
        );
        res.status(200).json(result);
    } catch (error) {
        console.error(error.response?.data || error.message);
        res.status(500).json({ error: "Xử lý callback thất bại" });
    }
};

module.exports = {
    createPayment,
    handlePaymentCallback,
};
