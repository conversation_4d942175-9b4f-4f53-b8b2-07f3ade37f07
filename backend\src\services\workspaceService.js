const { get } = require("https");
const { Workspace, Board, User, TaskList, Task } = require("../models");
const { v4: uuidv4 } = require("uuid");

const createWorkspace = async (name, description, ownerId) => {
    try {
        let workspace;
        if (!name) {
            throw new Error("Tên workspace là bắt buộc");
        }

        const user = await User.findByPk(ownerId);

        if (user.role === "admin") {
            workspace = await Workspace.create({
                name,
                description: description || "",
                ownerId,
                packageId: 3,
            });
        } else {
            workspace = await Workspace.create({
                name,
                description: description || "",
                ownerId,
            });
        }
        return workspace;
    } catch (error) {
        throw new Error(`Lỗi khi tạo workspace: ${error.message}`);
    }
};

const getWorkspacesByOwner = async (ownerId) => {
    try {
        const workspaces = await Workspace.findAll({
            where: { ownerId },
            order: [["created_at", "ASC"]],
        });

        return workspaces;
    } catch (error) {
        throw new Error(`Lỗi khi lấy danh sách workspace: ${error.message}`);
    }
};

const getWorkspaceById = async (id, ownerId) => {
    try {
        const workspace = await Workspace.findOne({
            where: {
                id,
                ownerId,
            },
            include: [
                {
                    model: Board,
                    as: "boards",
                    attributes: ["id", "name", "isGoogleSynced"],
                },
                {
                    model: User,
                    as: "owner",
                    attributes: ["id", "email", "fullName"],
                },
            ],
        });

        if (!workspace) {
            throw new Error(
                "Workspace không tồn tại hoặc bạn không có quyền truy cập"
            );
        }

        return workspace;
    } catch (error) {
        throw new Error(`Lỗi khi lấy thông tin workspace: ${error.message}`);
    }
};

const getWorkspacePackage = async (workspaceId) => {
    try {
        const workspace = await Workspace.findByPk(workspaceId);
        return workspace.packageId;
    } catch {
        throw new Error(
            `Lỗi khi lấy thông tin gói workspace: ${error.message}`
        );
    }
};

const updateWorkspace = async (id, updateData, ownerId) => {
    try {
        const workspace = await Workspace.findOne({
            where: {
                id,
                ownerId,
            },
        });

        if (!workspace) {
            throw new Error(
                "Workspace không tồn tại hoặc bạn không có quyền chỉnh sửa"
            );
        }

        await workspace.update(updateData);
        return workspace;
    } catch (error) {
        throw new Error(`Lỗi khi cập nhật workspace: ${error.message}`);
    }
};

const deleteWorkspace = async (id, ownerId) => {
    try {
        const workspace = await Workspace.findOne({
            where: {
                id,
                ownerId,
            },
        });

        if (!workspace) {
            throw new Error(
                "Workspace không tồn tại hoặc bạn không có quyền xóa"
            );
        }

        // Xóa tất cả các board trong workspace
        await Board.destroy({
            where: {
                workspaceId: id,
            },
        });

        // Sau khi xóa tất cả board, xóa workspace
        await workspace.destroy();
        return { message: "Workspace và tất cả board đã được xóa thành công" };
    } catch (error) {
        throw new Error(`Lỗi khi xóa workspace: ${error.message}`);
    }
};

const generatePublicShareId = async () => {
    try {
        let publicShareId;
        let isUnique = false;

        // Đảm bảo tạo ra ID duy nhất
        while (!isUnique) {
            publicShareId = uuidv4();
            const existingWorkspace = await Workspace.findOne({
                where: {
                    publicShareId,
                },
            });
            if (!existingWorkspace) {
                isUnique = true;
            }
        }

        return publicShareId;
    } catch (error) {
        throw new Error(`Lỗi khi tạo ID chia sẻ công khai: ${error.message}`);
    }
};

const enablePublicShare = async (workspaceId) => {
    try {
        const workspace = await Workspace.findOne({
            where: { id: workspaceId },
        });
        if (!workspace) {
            throw new Error("Workspace không tồn tại");
        }

        // Nếu chưa có publicShareId thì tạo mới
        if (!workspace.publicShareId) {
            workspace.publicShareId = await generatePublicShareId();
        }

        workspace.isPublicShareEnabled = true;
        await workspace.save();
        return workspace;
    } catch (error) {
        throw new Error(
            `Lỗi khi kích hoạt chia sẻ công khai: ${error.message}`
        );
    }
};

const disablePublicShare = async (workspaceId) => {
    try {
        const workspace = await Workspace.findOne({
            where: { id: workspaceId },
        });
        if (!workspace) {
            throw new Error("Workspace không tồn tại");
        }
        workspace.isPublicShareEnabled = false;
        await workspace.save();
        return workspace;
    } catch (error) {
        throw new Error(
            `Lỗi khi vô hiệu hóa chia sẻ công khai: ${error.message}`
        );
    }
};

const getPublicWorkspace = async (publicShareId) => {
    try {
        const workspace = await Workspace.findOne({
            where: {
                publicShareId,
                isPublicShareEnabled: true,
            },
            include: [
                {
                    model: Board,
                    as: "boards",
                    attributes: ["id", "name", "isGoogleSynced"],
                },
                {
                    model: User,
                    as: "owner",
                    attributes: ["id", "email", "fullName", "photoUrl"],
                },
            ],
        });

        if (!workspace) {
            throw new Error(
                "Workspace không tồn tại hoặc không được chia sẻ công khai"
            );
        }

        return workspace;
    } catch (error) {
        throw new Error(
            `Lỗi khi truy cập workspace công khai: ${error.message}`
        );
    }
};

const getPublicBoardTaskLists = async (publicShareId, boardId) => {
    try {
        // Kiểm tra workspace có được chia sẻ công khai không
        const workspace = await Workspace.findOne({
            where: {
                publicShareId,
                isPublicShareEnabled: true,
            },
            include: [
                {
                    model: Board,
                    as: "boards",
                    where: { id: boardId },
                    required: true,
                },
            ],
        });

        if (!workspace) {
            throw new Error(
                "Workspace không tồn tại, không được chia sẻ công khai hoặc board không thuộc về workspace này"
            );
        }

        // Lấy tasklists của board với tasks

        const taskLists = await TaskList.findAll({
            where: { boardId: boardId },
            include: [
                {
                    model: Task,
                    as: "tasks",
                    order: [["position", "ASC"]],
                },
            ],
            order: [["position", "ASC"]],
        });

        return taskLists;
    } catch (error) {
        throw new Error(
            `Lỗi khi truy cập tasklists công khai: ${error.message}`
        );
    }
};

module.exports = {
    createWorkspace,
    getWorkspacesByOwner,
    getWorkspaceById,
    getWorkspacePackage,
    updateWorkspace,
    deleteWorkspace,
    generatePublicShareId,
    enablePublicShare,
    disablePublicShare,
    getPublicWorkspace,
    getPublicBoardTaskLists,
};
