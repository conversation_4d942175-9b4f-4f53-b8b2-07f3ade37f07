import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import { Outlet, useLocation } from "react-router-dom";
import Home from "./pages/Home";
import DashBoard from "./pages/DashBoard";
import PublicWorkspace from "./pages/PublicWorkspace";
import TaskAssign from "./components/Task/TaskAssign";
import Features from "./components/Subscription/Features";
import Pricing from "./components/Subscription/Pricing";
import NotFound from "./pages/NotFound";
import MainLayout from "./layouts/MainLayout";
import { MessageProvider } from "./contexts/MessageContext";
import { AuthProvider } from "./contexts/AuthContext";
import { SocketProvider } from "./contexts/SocketContext";

// Layout wrapper component để sử dụng MainLayout với Outlet
const DashboardLayout = () => {
    return (
        <MainLayout>
            <Outlet />
        </MainLayout>
    );
};

function App() {
    return (
        <MessageProvider>
            <SocketProvider>
                <BrowserRouter>
                    <AuthProvider>
                        <Routes>
                            <Route path="/" element={<Home />} />
                            <Route path="/features" element={<Features />} />
                            <Route path="/pricing" element={<Pricing />} />
                            {/* Redirect từ route cũ đến route mới */}
                            <Route
                                path="/dashboard"
                                element={
                                    <Navigate to="/app/dashboard" replace />
                                }
                            />

                            {/* Public workspace route - không cần authentication */}
                            <Route
                                path="/public/workspace/:publicShareId"
                                element={<PublicWorkspace />}
                            />

                            {/* Nested routes với MainLayout */}
                            <Route path="/app" element={<DashboardLayout />}>
                                {/* Default redirect từ /app đến /app/dashboard */}
                                <Route
                                    index
                                    element={
                                        <Navigate to="dashboard" replace />
                                    }
                                />
                                <Route
                                    path="dashboard"
                                    element={<DashBoard />}
                                />
                                <Route
                                    path="task-assign"
                                    element={<TaskAssign />}
                                />
                            </Route>
                            {/* Route cho các trang không tồn tại */}
                            <Route path="*" element={<NotFound />} />
                        </Routes>
                    </AuthProvider>
                </BrowserRouter>
            </SocketProvider>
        </MessageProvider>
    );
}

export default App;
