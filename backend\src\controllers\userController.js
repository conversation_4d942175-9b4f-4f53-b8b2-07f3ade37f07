const userService = require("../services/userService");

const getCurrentUser = async (req, res) => {
    try {
        // req.user đượ<PERSON> set bởi middleware isAuthenticated
        if (!req.user) {
            return res.status(401).json({ message: "User not authenticated" });
        }

        // Trả về thông tin user hiện tạ<PERSON> (loại bỏ sensitive data)
        const { googleAccessToken, googleRefreshToken, ...userInfo } =
            req.user.toJSON();
        res.status(200).json(userInfo);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
};

const searchUsers = async (req, res) => {
    try {
        const query = req.query.q;
        const currentUserId = req.user?.id; // Lấy ID của user hiện tại
        console.log("Searching for:", query, "excluding user:", currentUserId);
        const users = await userService.searchUsers(query, currentUserId);
        res.status(200).json(users);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
};

const getAllUsers = async (req, res) => {
    try {
        const users = await userService.getAllUsers();
        res.status(200).json(users);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
};

const getUserById = async (req, res) => {
    try {
        const { userId } = req.params;
        const user = await userService.getUserById(userId);
        res.status(200).json(user);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
};

const getUserByEmail = async (req, res) => {
    try {
        const { email } = req.params;
        const user = await userService.getUserByEmail(email);
        res.status(200).json(user);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
};

module.exports = {
    getCurrentUser,
    searchUsers,
    getAllUsers,
    getUserById,
    getUserByEmail,
};
