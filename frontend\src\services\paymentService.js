import axios from "../utils/axiosCustomize";

const createPayment = async (
    workspaceId,
    packageId,
    packageDuration,
    amount
) => {
    const response = await axios.post(`/payments`, {
        workspaceId,
        packageId,
        packageDuration,
        amount,
    });
    return response.data;
};

const handlePaymentCallback = async (invoiceId) => {
    const response = await axios.post(`/payments/callback`, { invoiceId });
    return response.data;
};

export default {
    createPayment,
    handlePaymentCallback,
};
